import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ChangeDetectorRef, Component, OnInit, ViewEncapsulation } from '@angular/core';
import { isUndefined } from 'is-what';
import { Subscription } from 'rxjs';
import { AUDIT_SYSTEM_PK_ID, DateTimeDisplayFormat, ITEMS_PER_PAGE, ListAuditResource } from 'src/app/app.constants';
import { AuditListResponse } from 'src/app/model/Audit/AuditListResponse';
import { AuditFilterAction } from 'src/app/model/Audit/auditFilterAction';
import { AuditListPageResponse } from 'src/app/model/Audit/auditListPageResponse';
import { AuditSearchRequsetBody } from 'src/app/model/Audit/auditSearchRequsetBody';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { AuditApiCallService } from 'src/app/shared/Service/Audit/audit-api-call.service';
import { AuditService } from 'src/app/shared/Service/Audit/audit.service';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { AuditActionEnum } from 'src/app/shared/enum/Audit/AuditActionEnum.enum';
import { AuditModuleEnum } from 'src/app/shared/enum/Audit/AuditModuleEnum.enum';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { collapseFilterTextEnum } from 'src/app/shared/enum/collapseFilterButtonText.enum';
import { PermissionService } from 'src/app/shared/permission.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';

@Component({
  selector: 'app-auditlist',
  templateUrl: './auditlist.component.html',
  styleUrls: ['./auditlist.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class AuditlistComponent implements OnInit {
  loading: boolean = false;

  //Page
  itemsPerPage: number = 0;
  page: number = 0;
  previousPage: number = 0;
  totalItems: number = 0;

  //Totel Count Display
  totalRecordDisplay: number = 0;
  totalRecord: number = 0;

  //Page Size DropDown
  dataSizes: string[] = [];
  drpselectsize: number = ITEMS_PER_PAGE;
  dateTimeDisplayFormat: string = DateTimeDisplayFormat;
  listAuditResource = ListAuditResource;


  //Filter
  isFilterComponentInitWithApicall: boolean = true;
  listPageRefreshForbackToDetailPage: boolean = false;
  isFilterHidden: boolean = true;
  hideShowFilterButtonText: string = collapseFilterTextEnum.HIDE_FILTER;

  //audit List
  auditListResponse: AuditListResponse[] = [];

  //archive
  archivedAuditSearch: boolean;

  //subscription
  subscriptionForLoading: Subscription;
  subscriptionForAuditListFilterRequestParameter: Subscription;

  //audit serach request body store
  auditSearchRequsetBody: AuditSearchRequsetBody = null;

  viewDetailPageDisplay = this.hideShowDetailPage(null);
  //Audit List page hide/show
  listingPageDisplay = true;
  //Module Id used For Move In Detail page
  moduleDetailId = null;
  //Module Unique Id used For Move In Detail page
  moduleDetailUniqueId = null;
  auditModuleEnum = AuditModuleEnum;
  auditAction;
  otsKitManagementAction = [AuditActionEnum.OTS_KIT_MANGEMENT_ADD_IMPORT_CSV, AuditActionEnum.OTS_KIT_MANGEMENT_DELETE_IMPORT_CSV];
  bridgeKitManagementAction = [AuditActionEnum.BRIDGE_KIT_MANGEMENT_ADD_IMPORT_CSV, AuditActionEnum.BRIDGE_MANGEMENT_DELETE_IMPORT_CSV];
  otsKitManagementPermission: boolean = false;
  brigeKitManagementPermission: boolean = false;

  modulePermissionMapping: Map<string, boolean>;

  //Audit System Pk (Note : Detail Page Not display)
  auditSystemPrimeryId = AUDIT_SYSTEM_PK_ID;


  constructor(
    private authservice: AuthJwtService,
    private commonsService: CommonsService,
    private commonOperationsService: CommonOperationsService,
    private auditService: AuditService,
    private auditApiCallService: AuditApiCallService,
    private exceptionService: ExceptionHandlingService,
    private permissionService: PermissionService,
    private cdr: ChangeDetectorRef
  ) { }

  /**
   * <AUTHOR>
   */
  public ngOnInit(): void {
    if (!this.authservice.isAuthenticate()) {
      this.authservice.loginNavigate();
    } else {
      this.page = 0;
      this.dataSizes = this.commonsService.accessDataSizes();
      this.isFilterComponentInitWithApicall = true;
      this.listPageRefreshForbackToDetailPage = false;
      this.isFilterHidden = false;
      this.itemsPerPage = ITEMS_PER_PAGE;
      this.drpselectsize = ITEMS_PER_PAGE;
      this.modulePermissionMapping = this.auditService.getModulePermissionMapping();
      this.refreshFilter();
    }
    this.subjectInit();
    this.otsKitManagementPermission = this.permissionService.getKitManagementPermission(PermissionAction.GET_OTS_KIT_MANAGEMENT_ACTION);
    this.brigeKitManagementPermission = this.permissionService.getKitManagementPermission(PermissionAction.IMPORT_BRIDGE_KIT_CSV_ACTION);
  }

  private subjectInit(): void {
    /**
     * Loading Hide/Display
     * <AUTHOR>
     */
    this.subscriptionForLoading = this.commonOperationsService.getCommonLoadingSubject()?.subscribe((res: boolean) => {
      this.setLoadingStatus(res);
    });

    /**
     * This Subject call from Filter component
     * Load all the Data
     * <AUTHOR>
     */
    this.subscriptionForAuditListFilterRequestParameter = this.auditService.getAuditListFilterRequestParameterSubject()?.subscribe((auditFilterAction: AuditFilterAction) => {
      this.archivedAuditSearch = auditFilterAction.archivedAuditDataSearch;
      if (auditFilterAction.listingPageReloadSubjectParameter.isReloadData) {
        if (auditFilterAction.listingPageReloadSubjectParameter.isDefaultPageNumber) {
          this.resetPage();
        }
        this.loadAll(auditFilterAction.auditSearchRequsetBody);
      }
    });
  }

  /**
   * Destroy subscription
   * <AUTHOR>
   */
  public ngOnDestroy(): void {
    if (!isUndefined(this.subscriptionForLoading)) { this.subscriptionForLoading.unsubscribe() }
    if (!isUndefined(this.subscriptionForAuditListFilterRequestParameter)) { this.subscriptionForAuditListFilterRequestParameter.unsubscribe() }
    this.auditService.setAuditModuleListInStore([]);
  }

  /**
   * Reset Page
   * <AUTHOR>
   */
  private resetPage(): void {
    this.page = 0;
    this.previousPage = 1;
  }

  /**
   * Clear all filter ,Reset Page and Reload the page
   * <AUTHOR>
   */
  public refreshFilter(): void {
    this.resetPage();
    this.filterPageSubjectCallForReloadPage(true, true);
  }

  /**
   * Item par page Value Changes like (10,50,100)
   * <AUTHOR>
   * @param datasize 
   */
  public changeDataSize(datasize): void {
    this.setLoadingStatus(true);
    this.itemsPerPage = datasize.target.value;
    this.filterPageSubjectCallForReloadPage(true, false);
  }


  /**
   * Change The Page
   * callSalesOrderListRefreshSubject ->Call the filter component
   * filter not clear and send with filter requrest and load data 
   * <AUTHOR>
   * @param page 
   */
  public loadPage(page): void {
    if (page !== this.previousPage) {
      this.previousPage = page;
      this.filterPageSubjectCallForReloadPage(false, false);
    }
  }

  /**
   * Call Filter component subject and reload page
   * <AUTHOR>
   * @param isDefaultPageNumber 
   * @param isClearFilter 
   */
  public filterPageSubjectCallForReloadPage(isDefaultPageNumber: boolean, isClearFilter: boolean): void {
    let listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, isDefaultPageNumber, isClearFilter, false)
    this.auditService.callRefreshPageSubject(listingPageReloadSubjectParameter, ListAuditResource, this.isFilterHidden, this.auditSearchRequsetBody, this.archivedAuditSearch);
  }

  /**
   * Toggle Filter
   * <AUTHOR>
   * @param id 
   */
  public toggleFilter(): void {
    this.isFilterComponentInitWithApicall = false;
    this.listPageRefreshForbackToDetailPage = false;
    this.isFilterHidden = !this.isFilterHidden;
    if (this.isFilterHidden) {
      this.hideShowFilterButtonText = collapseFilterTextEnum.SHOW_FILTER;
    } else {
      this.hideShowFilterButtonText = collapseFilterTextEnum.HIDE_FILTER;
    }
  }

  /**
   * Get Audit List
   * 
   * <AUTHOR>
   * 
   * @param auditSearchRequsetBody 
   */
  public loadAll(auditSearchRequsetBody: AuditSearchRequsetBody): void {
    this.auditSearchRequsetBody = auditSearchRequsetBody;
    let pageObj = {
      page: this.page - 1,
      size: this.itemsPerPage
    }
    this.setLoadingStatus(true);
    this.auditApiCallService.getAuditList(auditSearchRequsetBody, pageObj, this.archivedAuditSearch)
      ?.subscribe(
        {
          next: (auditListPageResponse: HttpResponse<AuditListPageResponse>) => {
            if (auditListPageResponse.status == 200) {
              this.paginateDataset(auditListPageResponse.body);
            } else {
              this.auditListResponse = [];
              this.totalRecordDisplay = 0;
              this.totalRecord = 0;
            }
            this.setLoadingStatus(false);
          },
          error: (error: HttpErrorResponse) => {
            this.setLoadingStatus(false);
            this.exceptionService.customErrorMessage(error);
          }
        }
      );
  }

  /**
   * Audit List Page Response 
   * <AUTHOR>
   * @param auditListPageResponse 
   */
  private paginateDataset(auditListPageResponse: AuditListPageResponse): void {
    this.totalItems = auditListPageResponse.totalElements;
    this.auditListResponse = auditListPageResponse.content;
    this.page = auditListPageResponse.number + 1;
    this.totalRecord = auditListPageResponse.totalElements;
    this.totalRecordDisplay = auditListPageResponse.numberOfElements;
    this.setLoadingStatus(false);
  }



  /**
   * Loading Status 
   * <AUTHOR>
   */
  private setLoadingStatus(status: boolean): void {
    this.loading = status;
    this.cdr.detectChanges();
  }

  /**
   * View Detail Model
   * 
   * <AUTHOR>
   * @param auditResponse 
   */
  public auditViewDetail(auditResponse: AuditListResponse): void {
    this.auditService.openAuditDetailPopup(auditResponse).then((res: boolean) => {
      if (res) {
        this.filterPageSubjectCallForReloadPage(false, false);
      }
    }).finally(() => { });
  }

  /**
   * Open View Detail Page
   * 
   * <AUTHOR>
   * 
   * @param module 
   * @param viewDetail 
   * @param moduleDetailId 
   * @param moduleDetailUniqueId 
   */
  public openViewDetailPage(module: string, moduleDetailId: number, moduleDetailUniqueId: string, auditAction: string): void {
    this.isFilterComponentInitWithApicall = false;
    this.listPageRefreshForbackToDetailPage = true;
    this.moduleDetailId = moduleDetailId;
    this.moduleDetailUniqueId = moduleDetailUniqueId;
    this.auditAction = auditAction;
    let isPermission = (module == null) ? true : this.modulePermissionMapping?.get(module);
    if (isPermission) {
      this.viewDetailPageDisplay = this.hideShowDetailPage(module);
      this.listingPageDisplay = (module == null);
    }
    if (this.isFilterHidden && this.listingPageDisplay) {
      this.filterPageSubjectCallForReloadPage(true, false);
    }
  }

  /**
   * Hide/Show Detail page
   * <AUTHOR>
   * @param auditModuleEnum 
   * @returns 
   */
  private hideShowDetailPage(auditModuleEnum: string): any {
    let detailPageObject = {}
    for (let module in AuditModuleEnum) {
      if (AuditModuleEnum[module] == auditModuleEnum) {
        detailPageObject[AuditModuleEnum[module]] = true;
      } else {
        detailPageObject[AuditModuleEnum[module]] = false;
      }
    }
    return detailPageObject;
  }

}
