import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TransferOrderModuleComponent } from './transfer-order-module.component';
import { TransferOrderSelectionAndReviewComponent } from '../transfer-order-selection-and-review/transfer-order-selection-and-review.component';
import { TransferOrderReviewComponent } from '../transfer-order-review/transfer-order-review.component';
import { SalesOrderApiCallService } from 'src/app/shared/Service/SalesOrderService/sales-order-api-call.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { TransferOrderSelectionComponent } from '../transfer-order-selection/transfer-order-selection.component';
import { TransferOrderProductReviewResponse } from 'src/app/model/SalesOrder/TransferOrderProductReviewResponse.model';
import { SalesOrderTransferValidateRequest } from 'src/app/model/SalesOrder/SalesOrderTransferValidateRequest.model';
import { ReactiveFormsModule } from '@angular/forms';

describe('TransferOrderModuleComponent', () => {
  let component: TransferOrderModuleComponent;
  let fixture: ComponentFixture<TransferOrderModuleComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [TransferOrderModuleComponent, TransferOrderSelectionComponent, TransferOrderSelectionAndReviewComponent, TransferOrderReviewComponent],
      imports: [NgMultiSelectDropDownModule.forRoot(), ReactiveFormsModule],
      providers: [
        SalesOrderApiCallService,
        ExceptionHandlingService,
        AuthJwtService,
        LocalStorageService,
        SessionStorageService,
        commonsProviders(null)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(TransferOrderModuleComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should toggle transfer order pages correctly', () => {
    component.tansferOrderPagesToggle(false, true, false);
    expect(component.transferOrderSelectionDisaplay).toBeFalse();
    expect(component.transferOrderValidationDisaplay).toBeTrue();
    expect(component.transferOrderReviewDisaplay).toBeFalse();
  });

  it('should update the destination sales order ID', () => {
    component.updateDestinationId(12345);
    expect(component.destinationSalesOrderId).toBe(12345);
  });

  it('should set the transfer order request', () => {
    const requestMock: SalesOrderTransferValidateRequest = new SalesOrderTransferValidateRequest(null, null, null, null, null);
    component.selectedProduct(requestMock);
    expect(component.sourceSelectedProbeAndDevice).toBe(requestMock);
  });

  it('should set the transfer order review response', () => {
    const requestMock: SalesOrderTransferValidateRequest = new SalesOrderTransferValidateRequest(null, null, null, null, null);
    component.transferOrderReviewRequest(requestMock);
    expect(component.transferOrderRequest).toBe(requestMock);
  });

  it('should emit event when navigating back to detail page', () => {
    spyOn(component.showTranferOrder, 'emit');
    component.backToDetailPage();
    expect(component.showTranferOrder.emit).toHaveBeenCalled();
  });
});
