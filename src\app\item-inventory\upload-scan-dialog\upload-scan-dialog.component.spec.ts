import { HttpResponse } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { UploadFileChunckProperty } from 'src/app/model/SoftwaarBuilds/UploadFileChunckProperty.model';
import { UploadPackageRequest } from 'src/app/model/upload.package.request';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { InventoryService } from 'src/app/shared/inventory.service';
import { UserApiCallService } from 'src/app/shared/Service/user/user-api-call.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { DownloadService } from 'src/app/shared/util/download.service';
import { MultiSelectDropDownSettingService } from 'src/app/shared/util/multi-select-drop-down-setting.service';
import { ValidationService } from 'src/app/shared/util/validation.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { UploadScanDialogComponent } from './upload-scan-dialog.component';

describe('UploadScanDialogComponent Full Coverage', () => {
  let component: UploadScanDialogComponent;
  let fixture: ComponentFixture<UploadScanDialogComponent>;
  let inventoryServiceMock: any;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;


  beforeEach(async () => {
    inventoryServiceMock = jasmine.createSpyObj('InventoryService', [
      'pushFileToStorage',
      'uploadFileToStorage',
      'commitFileToStorage',
      'updateFirmwareUploadStatus'
    ]);

    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);


    await TestBed.configureTestingModule({
      declarations: [UploadScanDialogComponent],
      imports: [FormsModule,
        ReactiveFormsModule,
        NgMultiSelectDropDownModule.forRoot()],
      providers: [NgbActiveModal,
        UserApiCallService,
        CommonsService,
        ExceptionHandlingService,
        AuthJwtService,
        DownloadService,
        LocalStorageService,
        SessionStorageService,
        MultiSelectDropDownSettingService,
        ValidationService,
        { provide: InventoryService, useValue: inventoryServiceMock },
        commonsProviders(toastrServiceMock)
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(UploadScanDialogComponent);
    component = fixture.componentInstance;

    // Inject @Input() data
    component.countryList = [{ id: 1, country: 'USA' } as CountryListResponse];
    component.jsonVersionList = [{ id: 123, name: 'v1' }];
    component.title = 'Upload';
    component.message = 'Test message';
    component.btnOkText = 'OK';
    component.btnCancelText = 'Cancel';

    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should handle file selection and call upload flow', () => {
    const zipFile = new File(['dummy zip'], 'test.zip', { type: 'application/zip' });
    const jsonFile = new File(['{ "version": 1 }'], 'info.json', { type: 'application/json' });
    const dataTransfer = new DataTransfer();
    dataTransfer.items.add(zipFile);
    dataTransfer.items.add(jsonFile);
    const event = { target: { files: dataTransfer.files } };

    spyOn(component['commonsService'], 'getIdsFromArray').and.returnValue([1]);
    spyOn(component['commonsService'], 'getIdFromJsonObject').and.returnValue(123);
    spyOn(component['commonsService'], 'checkNullFieldValue').and.callThrough();
    spyOn(component['commentservice'], 'validateInput').and.returnValue(null);

    component.form.controls['Title'].setValue('Title123');
    component.form.controls['Version'].setValue('v1.0');
    component.form.controls['country'].setValue([{ countryId: 1 }]);
    component.form.controls['jsonVersion'].setValue([component.jsonVersionList[0]]);
    component.form.controls['partNumber'].setValue('PN1234');

    component.handleFileInput(event);
    expect(component.fileSelectedState).toBeTrue();

    const uploadResponse = {
      body: {
        preSignedUrlForAttachmentFile: 'https://fakeurl/attachment',
        preSignedUrlForReleaseNoteFile: 'https://fakeurl/release'
      }
    };

    inventoryServiceMock.pushFileToStorage.and.returnValue(of(uploadResponse));
    inventoryServiceMock.uploadFileToStorage.and.returnValue(of({}));
    inventoryServiceMock.commitFileToStorage.and.returnValue(of({ status: 201 }));
    inventoryServiceMock.updateFirmwareUploadStatus.and.returnValue(of({}));

    // ✅ Prevent infinite recursion
    spyOn<any>(component, 'uploadFileInChunk').and.callFake(() => {
      return;
    });

    component.accept();

    expect(inventoryServiceMock.pushFileToStorage).toHaveBeenCalled();
    expect(inventoryServiceMock.uploadFileToStorage).toHaveBeenCalled();
  });


  it('should handle upload error gracefully', () => {
    const zipFile = new File(['dummy zip'], 'test.zip', { type: 'application/zip' });
    const jsonFile = new File(['{ "version": 1 }'], 'info.json', { type: 'application/json' });
    const dataTransfer = new DataTransfer();
    dataTransfer.items.add(zipFile);
    dataTransfer.items.add(jsonFile);

    component.form.controls['Title'].setValue('Title123');
    component.form.controls['Version'].setValue('v1.0');
    component.form.controls['country'].setValue([{ countryId: 1 }]);
    component.form.controls['jsonVersion'].setValue([component.jsonVersionList[0]]);
    component.form.controls['partNumber'].setValue('PN1234');
    component.selectedFiles = dataTransfer.files;

    inventoryServiceMock.pushFileToStorage.and.returnValue(throwError(() => new Error('Upload failed')));

    spyOn(component['exceptionService'], 'customErrorMessage');

    component.accept();

    expect(component['exceptionService'].customErrorMessage).toHaveBeenCalled();
  });

  it('should commit the file after uploading all chunks', () => {
    const dummyFile = new File(['abc'], 'dummy.txt', { type: 'text/plain' });
    const props = new UploadFileChunckProperty(0, 3, 3, dummyFile, 'block', []);
    const response = { preSignedUrlForAttachmentFile: 'https://fakeurl/attachment' };
    const request = null;

    component['encodeToBase64'] = (str: string) => btoa(str); // override for test

    inventoryServiceMock.commitFileToStorage.and.returnValue(of(new HttpResponse({ status: 201 })));
    inventoryServiceMock.updateFirmwareUploadStatus.and.returnValue(of(new HttpResponse({ body: {} })));
    spyOn(component['activeModal'], 'close');

    component['uploadFileInChunk'](props, response as any, 'v1.0', request);

    expect(component['inventory_service'].commitFileToStorage).toHaveBeenCalled();
    expect(component['inventory_service'].updateFirmwareUploadStatus).toHaveBeenCalled();
    expect(component['activeModal'].close).toHaveBeenCalledWith(true);
  });

  it('should upload a file chunk when totalBytesRemaining is greater than 0', () => {
    const file = new File(['1234567890'], 'test.txt', { type: 'text/plain' }); // 10 bytes
    const props = new UploadFileChunckProperty(1000, 0, 5, file, 'block', []);
    const response = { preSignedUrlForAttachmentFile: 'https://fakeurl/attachment' };
    const requestData = { attachmentSize: 1056 } as UploadPackageRequest;

    component['encodeToBase64'] = (str: string) => btoa(str);

    // prevent actual recursion
    const recursiveSpy = spyOn(component as any, 'uploadFileInChunk').and.stub();
    inventoryServiceMock.uploadFileToStorage.and.returnValue(of(new HttpResponse({ status: 200, body: {} })));

    component['uploadFileInChunk'](props, response as any, 'v1.0', requestData);

    expect(recursiveSpy).toHaveBeenCalled();
  });

  it('should handle error if updateFirmwareUploadStatus fails after commit', () => {
    const dummyFile = new File(['abc'], 'dummy.txt', { type: 'text/plain' });
    const props = new UploadFileChunckProperty(0, 3, 3, dummyFile, 'block', ['dummyBlock']);
    const response = { preSignedUrlForAttachmentFile: 'https://fakeurl/attachment' };
    const requestData = {} as UploadPackageRequest;

    component['encodeToBase64'] = (str: string) => btoa(str);
    inventoryServiceMock.commitFileToStorage.and.returnValue(of(new HttpResponse({ status: 500 })));
    inventoryServiceMock.updateFirmwareUploadStatus.and.returnValue(of(new HttpResponse({ body: {} })));
    component['uploadFileInChunk'](props, response as any, 'v1.0', requestData);

    expect(component['inventory_service'].commitFileToStorage).toHaveBeenCalled();
  });



});
