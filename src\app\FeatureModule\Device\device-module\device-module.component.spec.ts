import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { DeviceSearchRequest } from 'src/app/model/device/deviceSearchRequest.model';
import { DeviceOperationService } from '../DeviceService/Device-Operation/device-operation.service';
import { DeviceModuleComponent } from './device-module.component';

describe('DeviceModuleComponent', () => {
  let component: DeviceModuleComponent;
  let fixture: ComponentFixture<DeviceModuleComponent>;
  let deviceOperationServiceSpy: jasmine.SpyObj<DeviceOperationService>;

  beforeEach(async () => {
    const spy = jasmine.createSpyObj('DeviceOperationService', ['callRefreshPageSubject']);

    await TestBed.configureTestingModule({
      declarations: [DeviceModuleComponent],
      providers: [
        { provide: DeviceOperationService, useValue: spy }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    })
      .compileComponents();

    fixture = TestBed.createComponent(DeviceModuleComponent);
    component = fixture.componentInstance;
    deviceOperationServiceSpy = TestBed.inject(DeviceOperationService) as jasmine.SpyObj<DeviceOperationService>;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Update Methods', () => {
    it('should update deviceSearchRequestBody when called from child', () => {
      const testValue = new DeviceSearchRequest(['test'], 'active', null, null, null, null, null, null, null, null, null);
      component.deviceSearchRequestBody = null;
      component.updateDeviceSearchRequestBody(testValue);
      expect(component.deviceSearchRequestBody).toBe(testValue);
    });

    it('should update isFilterHidden when called from child', () => {
      const testValue = true;
      component.isFilterHidden = false;
      component.updateIsFilterHidden(testValue);
      expect(component.isFilterHidden).toBe(testValue);
    });
  });

  describe('showDevice', () => {
    it('should show device listing page and hide detail page', () => {
      component.isDeviceListingPageDisplay = false;
      component.isDeviceDetailPageDisplay = true;
      component.showDevice();
      expect(component.isDeviceListingPageDisplay).toBe(true);
      expect(component.isDeviceDetailPageDisplay).toBe(false);
      expect(deviceOperationServiceSpy.callRefreshPageSubject).toHaveBeenCalled();
    });
  });

  describe('showDeviceDetail', () => {
    it('should show device detail page and hide listing page', () => {
      component.isDeviceListingPageDisplay = true;
      component.isDeviceDetailPageDisplay = false;
      component.showDeviceDetail();
      expect(component.isDeviceListingPageDisplay).toBe(false);
      expect(component.isDeviceDetailPageDisplay).toBe(true);
    });
  });

  describe('getDeviceId', () => {
    it('should set deviceIdInput when called', () => {
      const testDeviceId = 123;
      component.getDeviceId(testDeviceId);
      expect(component.deviceIdInput).toBe(testDeviceId);
    });
  });

  describe('filterPageSubjectCallForReloadPage', () => {
    it('should call deviceOperationService.callRefreshPageSubject with correct parameters', () => {
      component.isFilterHidden = true;
      component.deviceSearchRequestBody = new DeviceSearchRequest(['test'], 'active', null, null, null, null, null, null, null, null, null);
      component.filterPageSubjectCallForReloadPage(true, false);
      expect(deviceOperationServiceSpy.callRefreshPageSubject).toHaveBeenCalled();
    });

    it('should handle null deviceSearchRequestBody', () => {
      component.isFilterHidden = false;
      component.deviceSearchRequestBody = null;
      component.filterPageSubjectCallForReloadPage(false, true);
      expect(deviceOperationServiceSpy.callRefreshPageSubject).toHaveBeenCalled();
    });
  });
});
