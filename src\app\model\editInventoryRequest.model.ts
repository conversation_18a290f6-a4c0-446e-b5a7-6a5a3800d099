import { Jsonlist } from "./video/jsonlist.model";

export class EditInventoryRequest {
  deviceTypes: string[];
  isActive: boolean;
  jsonMaster: Jsonlist;
  countryIds: Array<number>;
  partNumber: string;

  constructor($deviceTypes: string[], isActive: boolean, jsonMaster: Jsonlist, countryIds: Array<number>,
    partNumber: string) {
    this.deviceTypes = $deviceTypes;
    this.isActive = isActive;
    this.jsonMaster = jsonMaster;
    this.countryIds = countryIds;
    this.partNumber = partNumber;
  }
}
