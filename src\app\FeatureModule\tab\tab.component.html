<body class="bg-white">
    <!-- main container start -->
    <div class="col-md-12">
        <!-- main row start -->
        <div class="row">
            <!-- tab view and page entry with table start -->
            <div class="col-md-12">

                <!-- tabs list start -->
                <ul id="myTab" class="nav nav-tabs mt-2">
                    <!-- device tab start -->
                    <li class="nav-item" (click)="changeTab(tabActiveEnum?.deviceTab_Active)"
                        *ngIf="deviceRederPermission">
                        <a [className]="tabActiveObject['deviceTab_Active']?'nav-link active':'nav-link'"
                            id="datasetlink" data-toggle="tab">DEVICES</a>
                    </li>
                    <!-- device tab end -->
                    <!-- ots probe tab start -->
                    <li class="nav-item" (click)="changeTab(tabActiveEnum.probeTab_Active)"
                        *ngIf="probeReaderPermission">
                        <a [className]="tabActiveObject['probeTab_Active']?'nav-link active':'nav-link'" id="otsProbeId"
                            data-toggle="tab">PROBES</a>
                    </li>
                    <!-- ots probe tab end -->
                    <!-- jobs tab start -->
                    <li class="nav-item" (click)="changeTab(tabActiveEnum.jobTab_Active)" *ngIf="jobReaderPermission">
                        <a [className]="tabActiveObject['jobTab_Active']?'nav-link active':'nav-link'" id="project"
                            data-toggle="tab">JOBS</a>
                    </li>
                    <!-- jobs tab end -->
                    <!-- inventory tab start -->
                    <li class="nav-item" (click)="changeTab(tabActiveEnum.itemInventoryTab_Active)"
                        *ngIf="softwareBuildReaderPermission">
                        <a data-toggle="tab"
                            [className]="tabActiveObject['itemInventoryTab_Active']?'nav-link active':'nav-link'">SOFTWARE
                            BUILDS</a>
                    </li>
                    <!-- inventory tab end -->
                    <!-- logs tab start -->
                    <li class="nav-item" (click)="changeTab(tabActiveEnum.logTab_Active)" *ngIf="logReaderPermission">
                        <a data-toggle="tab"
                            [className]="tabActiveObject['logTab_Active']?'nav-link active':'nav-link'">LOGS</a>
                    </li>
                    <!-- logs tab end -->
                    <!-- users tab start -->
                    <li class="nav-item" (click)="changeTab(tabActiveEnum.userTab_Active)" *ngIf="userReaderPermission">
                        <a data-toggle="tab"
                            [className]="tabActiveObject['userTab_Active']?'nav-link active':'nav-link'">USERS</a>
                    </li>
                    <!-- users tab end -->
                    <li class="nav-item" (click)="changeTab(tabActiveEnum.videoTab_Active)"
                        *ngIf="videoReaderPermission">
                        <a data-toggle="tab"
                            [className]="tabActiveObject['videoTab_Active']?'nav-link active':'nav-link'">VIDEOS</a>
                    </li>
                    <!-- country tab start -->

                    <!-- country tab end -->

                    <!-- Role tab start -->
                    <li class="nav-item" (click)="changeTab(tabActiveEnum.roleTab_Active)" *ngIf="roleReaderPermission">
                        <a data-toggle="tab"
                            [className]="tabActiveObject['roleTab_Active']?'nav-link active':'nav-link'">ROLE</a>
                    </li>
                    <!-- Role tab end -->

                    <!-------------------->
                    <!---------KIT MANAGEMENT start----------->
                    <li class="nav-item" (click)="changeTab(tabActiveEnum.kitManagementTab_Active)"
                        *ngIf="kitManagementReaderPermission">
                        <a data-toggle="tab"
                            [className]="tabActiveObject['kitManagementTab_Active']?'nav-link active':'nav-link'">KIT
                            MANAGEMENT</a>
                    </li>
                    <!---------KIT MANAGEMENT start----------->
                    <!-------------------->

                    <!-- Sales Order tab start -->
                    <li class="nav-item" (click)="changeTab(tabActiveEnum.salesOrderTab_Active)"
                        *ngIf="salesOrderReaderPermission">
                        <a data-toggle="tab"
                            [className]="tabActiveObject['salesOrderTab_Active']?'nav-link active':'nav-link'">SALES
                            ORDER</a>
                    </li>
                    <!-- Sales Order tab end -->
                    <!--------------Country Tab-------------------->
                    <li class="nav-item" (click)="changeTab(tabActiveEnum.countryTab_Active)"
                        *ngIf="countryReaderPermission">
                        <a data-toggle="tab"
                            [className]="tabActiveObject['countryTab_Active']?'nav-link active':'nav-link'">COUNTRY
                        </a>
                    </li>
                    <!--------------Country Tab-------------------->

                    <!--------------PROBE Config GROUP Tab-------------------->
                    <li class="nav-item" (click)="changeTab(tabActiveEnum.probeConfigGroupTab_Active)"
                        *ngIf="probeConfigGroupPermission">
                        <a data-toggle="tab"
                            [className]="tabActiveObject['probeConfigGroupTab_Active']?'nav-link active':'nav-link'">PROBE
                            CONFIG GROUP
                        </a>
                    </li>
                    <!--------------PROBE Config GROUP Tab-------------------->

                    <!--------------Audit Tab-------------------->
                    <li class="nav-item" (click)="changeTab(tabActiveEnum.auditTab_Active)"
                        *ngIf="auditReaderPermission ">
                        <a data-toggle="tab"
                            [className]="tabActiveObject['auditTab_Active']?'nav-link active':'nav-link'">AUDIT
                        </a>
                    </li>
                    <!--------------Audit Tab-------------------->

                </ul>
                <!-- tabs list end -->

                <!-- device list content start -->
                <div class="card border-top-0">
                    <div class="card-body">
                        <div class="tab-content" *ngIf="tabActiveObject != null">

                            <!-- device list tab start -->
                            <div [className]="tabActiveObject['deviceTab_Active']?'tab-pane fade show active':'tab-pane fade'"
                                id="device" *ngIf="deviceRederPermission && tabActiveObject['deviceTab_Active']">
                                <app-device-module></app-device-module>
                            </div>
                            <!-- device list tab end -->
                            <!--OTS Tab-->
                            <div [className]="tabActiveObject['probeTab_Active']?'tab-pane fade show active':'tab-pane fade'"
                                id="ots" *ngIf="probeReaderPermission && tabActiveObject['probeTab_Active']">
                                <app-probe-module></app-probe-module>
                            </div>
                            <!--Second Tabs job-->
                            <div [className]="tabActiveObject['jobTab_Active']?'tab-pane fade show active':'tab-pane fade'"
                                id="job" *ngIf="jobReaderPermission && tabActiveObject['jobTab_Active']">
                                <app-device-acivities></app-device-acivities>
                            </div>
                            <!--Third Tabs itemInventory Start-->
                            <div [className]="tabActiveObject['itemInventoryTab_Active']?'tab-pane fade show active':'tab-pane fade'"
                                id="itemInventory"
                                *ngIf="softwareBuildReaderPermission && tabActiveObject['itemInventoryTab_Active']">
                                <app-item-inventory></app-item-inventory>
                            </div>
                            <!--Third Tabs itemInventory End-->
                            <!--Four Tabs log Start-->
                            <div [className]="tabActiveObject['logTab_Active']?'tab-pane fade show active':'tab-pane fade'"
                                id="log" *ngIf="logReaderPermission && tabActiveObject['logTab_Active']">
                                <app-device-log></app-device-log>
                            </div>
                            <!--four Tabs log End-->
                            <!--5 Tabs log Start-->
                            <div [className]="tabActiveObject['userTab_Active']?'tab-pane fade show active':'tab-pane fade'"
                                id="user" *ngIf="userReaderPermission && tabActiveObject['userTab_Active']">
                                <app-user-listing></app-user-listing>
                            </div>
                            <!--5 Tabs log End-->
                            <!--6 Tabs log Start-->
                            <div [className]="tabActiveObject['videoTab_Active']?'tab-pane fade show active':'tab-pane fade'"
                                id="videos" *ngIf="videoReaderPermission && tabActiveObject['videoTab_Active']">
                                <app-video></app-video>
                            </div>
                            <!--6 Tabs log End-->

                            <!--7 Tabs Role Start-->
                            <div [className]="tabActiveObject['roleTab_Active']?'tab-pane fade show active':'tab-pane fade'"
                                id="Role" *ngIf="roleReaderPermission && tabActiveObject['roleTab_Active']">
                                <app-role-list></app-role-list>
                            </div>
                            <!--7 Tabs Role End-->

                            <!--8 Tabs kitManagement Start-->
                            <div [className]="tabActiveObject['kitManagementTab_Active']?'tab-pane fade show active':'tab-pane fade'"
                                id="kitManagement"
                                *ngIf="kitManagementReaderPermission && tabActiveObject['kitManagementTab_Active']">
                                <app-kit-management-module></app-kit-management-module>
                            </div>
                            <!--8 Tabs kitManagement End-->

                            <!--9 Tabs Sales Order Start-->
                            <div [className]="tabActiveObject['salesOrderTab_Active']?'tab-pane fade show active':'tab-pane fade'"
                                id="salesOrder"
                                *ngIf="salesOrderReaderPermission && tabActiveObject['salesOrderTab_Active']">
                                <app-sales-order-list></app-sales-order-list>
                            </div>
                            <!--9 Tabs Sales Order End-->

                            <!--------------Country Tab-------------------->
                            <div [className]="tabActiveObject['countryTab_Active']?'tab-pane fade show active':'tab-pane fade'"
                                id="country" *ngIf="countryReaderPermission && tabActiveObject['countryTab_Active']">
                                <app-country-list></app-country-list>
                            </div>
                            <!--------------Country Tab-------------------->


                            <!--------------audit Tab-------------------->
                            <div [className]="tabActiveObject['auditTab_Active']?'tab-pane fade show active':'tab-pane fade'"
                                id="country" *ngIf="auditReaderPermission && tabActiveObject['auditTab_Active']">
                                <app-auditlist></app-auditlist>
                            </div>
                            <!--------------audit Tab-------------------->

                            <!-------------PROBE CONFIG GROUP Tab --------------->
                            <div [className]="tabActiveObject['probeConfigGroupTab_Active']?'tab-pane fade show active':'tab-pane fade'"
                                id="softwearPackage"
                                *ngIf="probeConfigGroupPermission && tabActiveObject['probeConfigGroupTab_Active']">
                                <app-probe-config-group-list></app-probe-config-group-list>
                            </div>
                            <!-------------PROBE CONFIG GROUP Tab --------------->
                            <div *ngIf="noDataDisplay">
                                <app-no-data-message></app-no-data-message>
                            </div>
                            <!--tab end-->
                        </div>
                    </div>
                </div>
                <!-- device list content end -->
            </div>
            <!-- tab view and page entry with table end -->
        </div>
        <!-- main row end -->
    </div>
    <!-- main container end -->
</body>