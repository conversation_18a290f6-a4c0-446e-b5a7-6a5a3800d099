import { CommonsService } from '../util/commons.service';
import { InventoryMappedDevicePipe } from './inventory-mapped-device.pipe';

describe('InventoryMappedDevicePipe', () => {
  let pipe: InventoryMappedDevicePipe;
  let mockCommonsService: jasmine.SpyObj<CommonsService>;

  beforeEach(() => {
    // Mock the CommonsService
    mockCommonsService = jasmine.createSpyObj('CommonsService', ['getDeviceTypeEnumToString']);
    pipe = new InventoryMappedDevicePipe(mockCommonsService);
  });

  it('should create an instance of the pipe', () => {
    expect(pipe).toBeTruthy();
  });
});
