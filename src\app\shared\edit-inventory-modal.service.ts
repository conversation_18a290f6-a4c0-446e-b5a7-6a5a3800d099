import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { EditInventoryComponent } from '../item-inventory/edit-inventory/edit-inventory.component';
import { Inventory } from '../model/inventory.model';
import { BasicModelConfig } from '../model/common/BasicModelConfig.model';
import { CountryListResponse } from '../model/Country/CountryListResponse.model';

@Injectable({
  providedIn: 'root'
})
export class EditInventoryModalService {

  constructor(
    private modalService: NgbModal
  ) { }

  public openEditInventoryModel(
    basicModelConfig: BasicModelConfig,
    inventory: Inventory,
    jsonVersionList: any[],
    countryList: CountryListResponse[],
    dialogSize: 'sm' | 'lg' = 'sm'): Promise<boolean> {
    const modalRef = this.modalService.open(EditInventoryComponent, { size: dialogSize, modalDialogClass: 'editItemInventoryModelSize' });
    modalRef.componentInstance.basicModelConfig = basicModelConfig;
    modalRef.componentInstance.inventory = inventory;
    modalRef.componentInstance.jsonVersionList = jsonVersionList;
    modalRef.componentInstance.countryList = countryList;
    return modalRef.result;
  }

}
