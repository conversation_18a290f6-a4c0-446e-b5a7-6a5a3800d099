import { DatePipe } from '@angular/common';
import { HttpResponse } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { CreateUpdateMultipleProbeComponent } from '../create-update-multiple-probe/create-update-multiple-probe.component';
import { TransferOrderModuleComponent } from '../../TransferOrder/transfer-order-module/transfer-order-module.component';
import { clickOnButtonOrCheckBox, commonsProviders, conformDialog, countryListResponse, featuresListResponse, mockError, presetListResponse, presetResponse, probeTypeResponse, selectOperationOption, testDropdownInteraction, testToggleFilter } from '../../../Tesing-Helper/test-utils';
import { COMMON_SELECT_FILTER, INTERNAL_SERVER_ERROR, Probe_Select_Message, Probe_Single_Select_Message } from '../../../app.constants';
import { ConfirmDialogService } from '../../../confirmationdialog/confirmation.service';
import { BasicSalesOrderDetailResponse } from '../../../model/SalesOrder/BasicSalesOrderDetailResponse.model';
import { BasicModelConfig } from '../../../model/common/BasicModelConfig.model';
import { SuccessMessageResponse } from '../../../model/common/SuccessMessageResponse.model';
import { Pageable } from '../../../model/common/pageable.model';
import { Sort } from '../../../model/common/sort.model';
import { CustomerAssociationRequest } from '../../../model/customer-association-request';
import { ProbeDetailPageResponse } from '../../../model/probe/ProbeDetailPageResponse.model';
import { ProbeDetailResponse } from '../../../model/probe/probeDetail.model';
import { OtsProbesDetailComponent } from '../ots-probes-detail/ots-probes-detail.component';
import { ExceptionHandlingService } from '../../../shared/ExceptionHandling.service';
import { CountryCacheService } from '../../../shared/Service/CacheService/countrycache.service';
import { JobService } from '../../../shared/Service/JobService/job.service';
import { PresetApiService } from '../../../shared/Service/PresetService/preset-api.service';
import { ProbeApiService } from '../../../shared/Service/ProbeService/probe-api.service';
import { RoleApiCallService } from '../../../shared/Service/RoleService/role-api-call.service';
import { SalesOrderApiCallService } from '../../../shared/Service/SalesOrderService/sales-order-api-call.service';
import { AuthJwtService } from '../../../shared/auth-jwt.service';
import { DeviceService } from '../../../shared/device.service';
import { ProductStatusEnum } from '../../../shared/enum/Common/ProductStatus.enum';
import { UpdateProbeTypeService } from '../../../shared/modalservice/Probe/update-probe-type.service';
import { CustomerAssociationService } from '../../../shared/modalservice/customer-association.service';
import { PermissionService } from '../../../shared/permission.service';
import { EnumMappingDisplayNamePipe } from '../../../shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { AssignConfigDisablePipe } from '../../../shared/pipes/Probe/assign-config-disable.pipe';
import { FeatureValidityOptionHideShowPipe } from '../../../shared/pipes/Probe/feature-validity-option-hide-show.pipe';
import { FeaturesBaseResponseDisplayPipe } from '../../../shared/pipes/Probe/features-base-response-display.pipe';
import { FeaturesCheckBoxPipe } from '../../../shared/pipes/Probe/features-checkbox.pipe';
import { FeaturesCustomEndDateDisplayPipe } from '../../../shared/pipes/Probe/features-customEndDateDisplay.pipe';
import { FeaturesExpireDateDisplayPipe } from '../../../shared/pipes/Probe/features-expire-datedisplay.pipe';
import { FeaturesRadioButtonPipe } from '../../../shared/pipes/Probe/features-radio-button.pipe';
import { FeaturesTextDisplayPipe } from '../../../shared/pipes/Probe/features-textdisplay.pipe';
import { FeaturesValidityPartNumberDisplayPipe } from '../../../shared/pipes/Probe/features-validity-partNumber-Display.pipe';
import { HidePermissionNamePipe } from '../../../shared/pipes/Role/hidePermissionName.pipe';
import { CommonBooleanValueDisplayPipe } from '../../../shared/pipes/common-boolean-value-display.pipe';
import { PrintListPipe } from '../../../shared/pipes/printList.pipe';
import { UpdateAssociationService } from '../../../shared/update-association.service';
import { CommonOperationsService } from '../../../shared/util/common-operations.service';
import { CommonsService } from '../../../shared/util/commons.service';
import { DownloadService } from '../../../shared/util/download.service';
import { KeyValueMappingServiceService } from '../../../shared/util/key-value-mapping-service.service';
import { OtsProbesComponent } from './ots-probes-list.component';
describe('OtsProbesComponent', () => {
  let component: OtsProbesComponent;
  let fixture: ComponentFixture<OtsProbesComponent>;
  let localStorageServiceMock: jasmine.SpyObj<LocalStorageService>;
  let authServiceSpy: jasmine.SpyObj<AuthJwtService>;
  let exceptionHandlingService: ExceptionHandlingService;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let permissionServiceSpy: jasmine.SpyObj<PermissionService>;
  let probeApiServiceSpy: jasmine.SpyObj<ProbeApiService>;
  let salesOrderApiCallServiceSpy: jasmine.SpyObj<SalesOrderApiCallService>;
  let countryCacheServiceSpy: jasmine.SpyObj<CountryCacheService>;
  let presetApiServiceSpy: jasmine.SpyObj<PresetApiService>;
  let customerAssociationServicespy: jasmine.SpyObj<CustomerAssociationService>;
  let confirmDialogServiceMock: jasmine.SpyObj<ConfirmDialogService>;
  let updateAssociationServiceMock: jasmine.SpyObj<UpdateAssociationService>;

  const content: Array<ProbeDetailResponse> = [
    new ProbeDetailResponse(2993, "DAUMFI#4", "Torso1, USB", "CW Doppler, Auto EF, Auto Doppler, TDI, Trio 2.0, PW Doppler, AI Fast", "Heart, Lungs Torso, Abdomen", "mohit", "eic_test", "4.0.1.0", "iOS", "18.2.1", 1740119282001, ProductStatusEnum.DISABLED, "Algeria", true, true),
    new ProbeDetailResponse(4117, "DAUMFI-3", "Torso1, USB", "CW Doppler", "Heart, Lungs Torso, Abdomen", "testasdf", "eic_test", "6.1.0.46", "Android", "13", 1740052274174, ProductStatusEnum.DISABLED, "Argentina", false, true),
    new ProbeDetailResponse(4090, "DAUR2004016-002", "Torso3", "AI Fast", "Heart, Lungs Torso, Abdomen", "mohit", "eic_test", "7.2.5.22", "Bridge", "7.2.5.22", 1738560235309, ProductStatusEnum.DISABLED, "Akshay443", true, false),
    new ProbeDetailResponse(4091, "DAUR2007008-001", "Torso3", "AI Fast", "Heart, Lungs Torso, Abdomen", "mohit", "eic_test", "8.0.1.4", "Bridge", "8.0.1.4", 1733467197426, ProductStatusEnum.DISABLED, "Ireland", false, false),
    new ProbeDetailResponse(3592, "DAUR2008003-010", "Torso3", "AI Fast", "Heart, Lungs Torso, Abdomen", "mohit", "eic_test", "8.0.2.1", "Bridge", "8.0.2.1", 1739526419986, ProductStatusEnum.DISABLED, "Akshay443", true, false),
    new ProbeDetailResponse(3638, "DAUR2008006-001", "Torso3", "AI Fast", "Heart, Lungs Torso, Abdomen", "mohit", "eic_test", "7.2.5.22", "Bridge", "7.2.5.22", 1737376517135, ProductStatusEnum.DISABLED, "Akshay443", true, false),
    new ProbeDetailResponse(3325, "dwcwdav", "Torso1", "AI Fast", "Heart, Lungs Torso, Abdomen", "mohit", "eic_test", "7.2.5.22", "Bridge", "7.2.5.22", 0, ProductStatusEnum.DISABLED, "Akshay443", true, false),
    new ProbeDetailResponse(3060, "ENG_L1AR2210020-02", "Lexsa", "AI Fast", "Nerve, Lungs Lexsa, Msk, Vascular", "maitri123", "T1Amaitri291120242", "7.2.5.22", "Android", "14", 1735638451041, ProductStatusEnum.DISABLED, "Algeria", false, false),
    new ProbeDetailResponse(2143, "ENG_Lexsa", "Lexsa", "PW Doppler", "Nerve, Lungs Lexsa, Msk, Vascular", "maitri123", "T1Amaitri291120242", "4.0.0.8.2", "iOS", "18.1.1", 1736493995786, ProductStatusEnum.DISABLED, "Algeria", true, false),
    new ProbeDetailResponse(3593, "ENG012", "Torso3", "AI Fast", "Heart, Lungs Torso, Abdomen", "mohit", "eic_test", "7.2.5.24", "Bridge", "7.2.5.24", 1729514478172, ProductStatusEnum.DISABLED, "Argentina", false, true),
  ]


  const sort = new Sort(true, false, false); // Unsigned, not sorted, not empty
  // Create the Pageable object
  const pageable = new Pageable(sort, 0, 10, 0, true, false);
  let ProbeListResponse: ProbeDetailPageResponse = new ProbeDetailPageResponse(pageable, 5, false, 50, 10, true, sort, 10, 0, false, content);

  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info', 'clear']);
    localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);
    confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['confirm', 'getBasicModelConfigForDisableAction', 'getBasicModelConfigForRMAAction']);
    updateAssociationServiceMock = jasmine.createSpyObj('UpdateAssociationService', ['openUpdateAssociationModel']);
    permissionServiceSpy = jasmine.createSpyObj('PermissionService', ['getProbPermission']);
    probeApiServiceSpy = jasmine.createSpyObj('ProbeApiService', ['getProbeDetailInfo', 'getProbeHistory', 'getAllProbes', 'getFilterValue', 'getProbeTypesList', 'getFeaturesList', 'getPresetsList', 'getProbeTypesList', 'editEnableDisableProbe', 'updateLockState', 'generateCSVFileForProbe', 'downloadCSVFileForProbe', 'deleteProbes', 'rmaProductStatusForProbe', 'disableProductStatusForProbe', 'getProbePresetsList', 'associationProbeWithSalesOrder', 'getprobeTypeResponseList', 'probeTypeUpdate', 'dowloadSasUriofFeatureLicenseAsync', 'getPresetsList']);
    authServiceSpy = jasmine.createSpyObj('AuthJwtService', ['isAuthenticate', 'loginNavigate']);
    countryCacheServiceSpy = jasmine.createSpyObj('CountryCacheServiceSpy', ['getCountryListFromCache']);
    salesOrderApiCallServiceSpy = jasmine.createSpyObj('SalesOrderApiCallService', ['getSalesOrderNumberList', 'getOrderRecordNumberList', 'getBasicSalesOrderDetails']);
    presetApiServiceSpy = jasmine.createSpyObj('PresetApiService', ['getProbePresetsList']);
    customerAssociationServicespy = jasmine.createSpyObj('CustomerAssociationService', ['openCustomerAssociationPopup']);
    await TestBed.configureTestingModule({
      declarations: [OtsProbesComponent, FeaturesTextDisplayPipe, CommonBooleanValueDisplayPipe, FeaturesValidityPartNumberDisplayPipe, TransferOrderModuleComponent, FeaturesCheckBoxPipe, FeaturesExpireDateDisplayPipe, FeaturesRadioButtonPipe, FeaturesCustomEndDateDisplayPipe, AssignConfigDisablePipe, FeaturesBaseResponseDisplayPipe, FeatureValidityOptionHideShowPipe, EnumMappingDisplayNamePipe, OtsProbesDetailComponent, CreateUpdateMultipleProbeComponent],
      imports: [NgbPaginationModule, NgMultiSelectDropDownModule.forRoot(), ReactiveFormsModule, FormsModule],
      providers: [
        JobService,
        DeviceService,
        DownloadService,
        ConfirmDialogService,
        CommonsService,
        LocalStorageService,
        ExceptionHandlingService,
        AuthJwtService,
        FeaturesValidityPartNumberDisplayPipe,
        SessionStorageService,
        CommonOperationsService,
        RoleApiCallService,
        EnumMappingDisplayNamePipe,
        UpdateProbeTypeService,
        HidePermissionNamePipe,
        DatePipe,
        KeyValueMappingServiceService,
        PrintListPipe,
        { provide: PermissionService, useValue: permissionServiceSpy },
        { provide: ProbeApiService, useValue: probeApiServiceSpy },
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        { provide: UpdateAssociationService, useValue: updateAssociationServiceMock },
        { provide: PresetApiService, useValue: presetApiServiceSpy },
        { provide: AuthJwtService, useValue: authServiceSpy },
        { provide: CountryCacheService, useValue: countryCacheServiceSpy },
        { provide: SalesOrderApiCallService, useValue: salesOrderApiCallServiceSpy },
        { provide: CustomerAssociationService, useValue: customerAssociationServicespy },
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        commonsProviders(toastrServiceMock)
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(OtsProbesComponent);
    component = fixture.componentInstance;
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService);
    fixture.detectChanges();
  });

  function mockProbeApiService(error: boolean = false, status: number = 200) {
    presetApiServiceSpy.getProbePresetsList?.and.returnValue(Promise.resolve(presetListResponse));
    probeApiServiceSpy.getFeaturesList?.and.returnValue(Promise.resolve(featuresListResponse));
    countryCacheServiceSpy.getCountryListFromCache?.and.returnValue(Promise.resolve(countryListResponse));
    probeApiServiceSpy.getprobeTypeResponseList?.and.returnValue(Promise.resolve(probeTypeResponse));
    updateAssociationServiceMock.openUpdateAssociationModel.and.returnValue(Promise.resolve(true));
    confirmDialogServiceMock.confirm.and.returnValue(Promise.resolve(true));
    confirmDialogServiceMock.getBasicModelConfigForDisableAction.and.returnValue(new BasicModelConfig("", "", "", ""));
    confirmDialogServiceMock.getBasicModelConfigForRMAAction.and.returnValue(new BasicModelConfig("", "", "", ""));
    salesOrderApiCallServiceSpy.getSalesOrderNumberList.and.returnValue(Promise.resolve(["00000508", "00000509", "00000513", "00000515", "00000516", "00000518", "00000519", "00000523"]));
    salesOrderApiCallServiceSpy.getOrderRecordNumberList.and.returnValue(Promise.resolve(["00000508", "00000509", "00000513", "00000515", "00000516", "00000518", "00000519", "00000523"]));
    probeApiServiceSpy.getPresetsList?.and.returnValue(Promise.resolve(presetResponse))
    probeApiServiceSpy.getProbeTypesList?.and.returnValue(of(new HttpResponse<any>({
      body: ["Lexsa", "Torso1", "Torso1, USB", "Torso3"],
      status: 200,
      statusText: 'OK',
    })));
    if (error) {
      probeApiServiceSpy.getAllProbes?.and.returnValue(throwError(() => mockError));
    } else {
      probeApiServiceSpy.getAllProbes?.and.returnValue(of(new HttpResponse<ProbeDetailPageResponse>({
        body: ProbeListResponse,
        status: status,
        statusText: 'OK',
      })));
    }

  }

  function mockPermission(country: string = "Algeria") {
    // === Authentication & Country Setup ===
    // Mock valid authentication and device permissions
    authServiceSpy.isAuthenticate?.and.returnValue(true);
    permissionServiceSpy.getProbPermission?.and.returnValue(true);
    // Simulate user with access limited to Japan and Argentina countries
    localStorageServiceMock.retrieve.and.returnValue([{ "country": country }, { "country": "Argentina" }]);

  }

  function mockProbeApi(action: any, message: string, error: boolean, status: number = 200) {
    if (error) {
      probeApiServiceSpy[action]?.and.returnValue(throwError(() => mockError));
    } else {
      probeApiServiceSpy[action]?.and.returnValue(of(new HttpResponse<SuccessMessageResponse>({
        body: {
          "message": message
        },
        status: status,
        statusText: 'OK',
      })));
    }
  }

  function mockProbeExportCSV(error: boolean = false) {
    if (error) {
      probeApiServiceSpy.generateCSVFileForProbe?.and.returnValue(throwError(() => mockError));
    } else {
      probeApiServiceSpy.generateCSVFileForProbe?.and.returnValue(of(new HttpResponse<any>({
        body: {
          "fileName": "Probe__1768.xls"  // Mock generated filename
        },
        status: 200,
        statusText: 'OK',
      })));
    }
  }

  function mockProbeDownloadCSV(error: boolean = false) {
    if (error) {
      probeApiServiceSpy.downloadCSVFileForProbe?.and.returnValue(throwError(() => mockError));
    } else {
      probeApiServiceSpy.downloadCSVFileForProbe?.and.returnValue(of(new HttpResponse<any>({
        body: "ghcxdzgxfchjkjhgfcxhrcgji",  // Mock file content
        status: 200,
        statusText: 'OK',
      })));
    }
  }

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should initialize component and handle UI interactions when authenticated', async () => {
      // === Authentication and Permission Setup ===
      // Simulate authenticated user with Probe permissions
      authServiceSpy.isAuthenticate?.and.returnValue(true);
      permissionServiceSpy.getProbPermission?.and.returnValue(true);

      // === Component Initialization ===
      component.ngOnInit();
      fixture.detectChanges();  // Trigger initial data binding
      await fixture.whenStable();  // Wait for initial async operations

      // === Refresh Button Test ===
      // Verify clickOnRefreshButton is called on button click
      spyOn(component, 'clickOnRefreshButton')?.and.callThrough();
      const button = fixture.nativeElement.querySelector('#refresh_ProbeList');
      expect(button).toBeTruthy();  // Ensure refresh button exists
      button?.click();
      expect(component.clickOnRefreshButton).toHaveBeenCalled();

      // === Dropdown Interaction Test ===
      // Validate entries per page dropdown functionality
      testDropdownInteraction(fixture, component, '#otsProbesShowEntry');

      // === Pagination Test ===
      // Verify page change event handling
      const paginationElement = fixture.debugElement.query(By.css('#probe-pagination'));
      paginationElement.triggerEventHandler('pageChange', 2);
      fixture.detectChanges();
      expect(component.page).toBe(2);  // Confirm page index update
    });

    // Test: Toggles filter visibility and updates the button text
    it('should toggle filter visibility and update the button text', () => {
      // Use the generic utility to test the toggle behavior
      testToggleFilter(component);
    });

    // Test: Verify component handles server errors appropriately
    it('should handle HTTP 500 error by showing toastr message and invoking custom error handler', async () => {
      // Arrange - Setup test conditions
      // Mock successful authentication and permissions
      authServiceSpy.isAuthenticate?.and.returnValue(true);
      permissionServiceSpy.getProbPermission?.and.returnValue(true);

      // Stub device service to return error observable
      probeApiServiceSpy.getAllProbes?.and.returnValue(throwError(() => mockError));

      // Spy on error handling service
      spyOn(exceptionHandlingService, 'customErrorMessage')?.and.callThrough();

      // Act - Trigger component initialization
      component.ngOnInit();
      fixture.detectChanges();
      await fixture.whenStable();  // Wait for async operations

      // Assert - Verify error handling behavior
      // Check custom error handler was called with the error
      expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalledWith(mockError);

      // Verify correct error message is displayed via toastr
      expect(toastrServiceMock?.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);
    });
  });

  it('should handle API errors and empty responses gracefully', async () => {
    // === Authentication & Permission Setup ===
    // Mock valid user authentication and permissions
    mockPermission();

    // === API Error Scenario Setup ===
    // Mock empty device list response (204 No Content)
    mockProbeApiService(true, 204);

    // === Filter Configuration ===
    // Set test filter value to simulate user input
    component.filterForm.get('deviceModel')?.setValue("123123456789045678901234567890123456789012345678900");

    // === Component Initialization ===
    component.loadAll();
    fixture.detectChanges();  // Trigger initial data binding
    await fixture.whenStable();  // Wait for async operations
    // === Error State Validation ===
    // Verify component handles empty response correctly
    expect(component.probes.length).toBe(0);          // Device list should be empty
    expect(component.totalProbes).toBe(0);         // Total count should reset to 0
    expect(component.totalProbeDisplay).toBe(0);  // Displayed items should be 0
  });

  it('should handle device edit enable and unlock operations with proper validation', async () => {
    // Mock user permissions for the test
    mockPermission();
    // Mock the CSV export functionality
    mockProbeExportCSV();
    // Mock the CSV download functionality
    mockProbeDownloadCSV();
    // Mock successful API responses for editing and unlocking probes
    mockProbeApiService();
    mockProbeApi('editEnableDisableProbe', "The Selected Probe is marked as editable. You can now make changes.", false);
    mockProbeApi('updateLockState', "Probe is Unlocked successfully.", false);

    // Initialize the component and trigger change detection
    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    // Test the edit enable operation
    await selectOperationOption(fixture, 1, '#probeOperation'); // Select the edit option
    expect(toastrServiceMock.info).toHaveBeenCalledWith(Probe_Select_Message); // Check if info message is shown

    // Select a specific probe and try to enable editing again
    await clickOnButtonOrCheckBox(fixture, "#probe_2993"); // Click on the probe checkbox
    await selectOperationOption(fixture, 1, '#probeOperation'); // Select the edit option again
    expect(toastrServiceMock.success).toHaveBeenCalledWith("The Selected Probe is marked as editable. You can now make changes."); // Verify success message

    // Reset the spy counts for toastr service
    toastrServiceMock.info.calls.reset();
    toastrServiceMock.success.calls.reset();

    // Test the unlock operation
    await selectOperationOption(fixture, 3, '#probeOperation'); // Select the unlock option
    expect(toastrServiceMock.info).toHaveBeenCalledWith(Probe_Select_Message); // Check if info message is shown

    // Select the same probe and try to unlock it again
    await clickOnButtonOrCheckBox(fixture, "#probe_2993"); // Click on the probe checkbox
    await selectOperationOption(fixture, 3, '#probeOperation'); // Select the unlock option

    // Test the Configer Licence operation
    await selectOperationOption(fixture, 5, '#probeOperation'); // Select the Configer Licence option
    expect(toastrServiceMock.info).toHaveBeenCalledWith(Probe_Select_Message); // Verify info message

    // Test the Custmore Sales Order operation
    await selectOperationOption(fixture, 6, '#probeOperation'); // Select the Custmore Sales Order option
    expect(toastrServiceMock.info).toHaveBeenCalledWith(Probe_Select_Message); // Verify info message

    // Test the Download Licence operation
    await selectOperationOption(fixture, 7, '#probeOperation'); // Select the Download Licence option
    expect(toastrServiceMock.info).toHaveBeenCalledWith(Probe_Select_Message); // Verify info message

    // Test the Delete Probe operation
    await selectOperationOption(fixture, 8, '#probeOperation'); // Select the Delete Probe option
    expect(toastrServiceMock.info).toHaveBeenCalledWith(Probe_Select_Message); // Verify info message

    // Test the Disable Probe operation
    await selectOperationOption(fixture, 9, '#probeOperation'); // Select the Disable Probe option
    expect(toastrServiceMock.info).toHaveBeenCalledWith(Probe_Select_Message); // Verify info message

    // Test the RMA Probe operation
    await selectOperationOption(fixture, 10, '#probeOperation'); // Select the RMA Probe option
    expect(toastrServiceMock.info).toHaveBeenCalledWith(Probe_Select_Message); // Verify info message

    // Test the Update Probe Type operation
    await selectOperationOption(fixture, 11, '#probeOperation'); // Select the Update Probe Type option
    expect(toastrServiceMock.info).toHaveBeenCalledWith(Probe_Select_Message); // Verify info message

    // Test the Import CSV operation
    await selectOperationOption(fixture, 12, '#probeOperation'); // Select the Import CSV option
    // Verify that the loading state is reset after the operation completes
    expect(component.loading).toBeFalsy(); // Ensure loading is false


  });

  it('should handle device edit enable and unlock operations with proper validation', async () => {
    // Mock user permissions for the test
    mockPermission();

    // Mock the CSV export functionality
    mockProbeExportCSV(true);

    // Mock the customer association popup with a resolved promise
    customerAssociationServicespy.openCustomerAssociationPopup?.and.returnValue(
      Promise.resolve(
        new CustomerAssociationRequest(
          true, true,
          new BasicSalesOrderDetailResponse(null, null, null, null, null, false, false, null)
        )
      )
    );

    // Mock API services for probe-related operations
    mockProbeApiService();
    mockProbeApi('editEnableDisableProbe', null, true); // Mock enabling/disabling a probe
    mockProbeApi('updateLockState', null, true); // Mock locking/unlocking a probe
    mockProbeApi('deleteProbes', null, true); // Mock deleting a probe
    mockProbeApi('rmaProductStatusForProbe', null, true); // Mock checking RMA status
    mockProbeApi('disableProductStatusForProbe', null, true); // Mock disabling product status
    mockProbeApi('associationProbeWithSalesOrder', null, true); // Mock associating a probe with a sales order
    mockProbeApi('probeTypeUpdate', null, true); // Mock updating probe type

    // Initialize the component and trigger change detection
    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    // Select a probe and attempt an operation (e.g., enabling/disabling)
    await clickOnButtonOrCheckBox(fixture, "#probe_2993");
    await selectOperationOption(fixture, 8, '#probeOperation');
    await conformDialog(fixture, '#updateAssociationOkBtn');

    // Validate error message for operation failure
    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);

    // Select the same probe and attempt to lock it
    await clickOnButtonOrCheckBox(fixture, "#probe_2993");
    await selectOperationOption(fixture, 4, '#probeOperation');

    // Select the same probe and attempt to set it as read-only
    await clickOnButtonOrCheckBox(fixture, "#probe_2993");
    await selectOperationOption(fixture, 2, '#probeOperation');

    // Validate error message for the failed operation
    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);

    // Attempt another operation on the same probe
    await clickOnButtonOrCheckBox(fixture, "#probe_2993");
    await selectOperationOption(fixture, 12, '#probeOperation');

    // Validate error message for the failed operation
    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);

    // Attempt different operations and validate errors
    await selectOperationOption(fixture, 10, '#probeOperation');
    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);

    await selectOperationOption(fixture, 9, '#probeOperation');
    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);

    // Select all probes and attempt an operation that requires a single selection
    await clickOnButtonOrCheckBox(fixture, "#selectAllProbe");
    await selectOperationOption(fixture, 5, '#probeOperation');

    // Validate warning message for multi-selection restriction
    expect(toastrServiceMock.info).toHaveBeenCalledWith(Probe_Single_Select_Message);

    // Deselect all probes and verify the selection is cleared
    await clickOnButtonOrCheckBox(fixture, "#selectAllProbe");
    expect(component.probeIdList.length).toBe(0);

    // Select a probe and attempt additional operations
    await clickOnButtonOrCheckBox(fixture, "#probe_2993");
    await selectOperationOption(fixture, 6, '#probeOperation');

    await clickOnButtonOrCheckBox(fixture, "#probe_2993");
    await selectOperationOption(fixture, 11, '#probeOperation');

    // Locate and interact with the probe type update component
    const updateProbeTypeComponentElement = document.querySelector('app-update-probe-type');

    if (updateProbeTypeComponentElement && window['ng']) {
      // Retrieve the Angular component instance
      const updateProbeTypeComponent = window['ng'].getComponent(updateProbeTypeComponentElement);

      // Update the probe type form value
      updateProbeTypeComponent.probeForm.get('probeType').setValue(1);

      fixture.detectChanges();
      await fixture.whenStable();

      // Confirm the update action
      await conformDialog(fixture, "#uploadBtn");

      // Validate error message for the failed update
      expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);
    }
  });


  it('should handle probe operations including enable, disable, edit, and validation correctly', async () => {
    // Mock user permissions for the test
    mockPermission();

    // Mock CSV export functionalities (disabling export, enabling download)
    mockProbeExportCSV(false);
    mockProbeDownloadCSV(true);

    // Mock the customer association popup to return a resolved promise with test data
    customerAssociationServicespy.openCustomerAssociationPopup?.and.returnValue(
      Promise.resolve(
        new CustomerAssociationRequest(
          true, true,
          new BasicSalesOrderDetailResponse(null, null, null, null, null, false, false, null)
        )
      )
    );

    // Mock API service for downloading SAS URI of feature license
    probeApiServiceSpy.dowloadSasUriofFeatureLicenseAsync?.and.returnValue(Promise.resolve(null));

    // Mock API services for various probe-related operations
    mockProbeApiService();
    mockProbeApi('deleteProbes', "Probe(s) deleted successfully.", false); // Mock deleting a probe
    mockProbeApi('rmaProductStatusForProbe', "Probe is successfully marked as RMA.", false); // Mock marking as RMA
    mockProbeApi('disableProductStatusForProbe', "Probe is successfully marked as Disable.", false); // Mock disabling probe
    mockProbeApi('associationProbeWithSalesOrder', "Sales Order details are updated successfully.", false); // Mock sales order association
    mockProbeApi('probeTypeUpdate', "Probe type updated successfully.", false); // Mock updating probe type

    // Initialize the component and trigger change detection
    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    // Select a probe and attempt a delete operation
    await clickOnButtonOrCheckBox(fixture, "#probe_2993");
    await selectOperationOption(fixture, 8, '#probeOperation'); // Selecting delete operation
    await conformDialog(fixture, '#updateAssociationOkBtn'); // Confirming deletion

    // Validate success message for deletion
    expect(toastrServiceMock.success).toHaveBeenCalledWith("Probe(s) deleted successfully.");

    // Attempt another operation on the same probe
    await clickOnButtonOrCheckBox(fixture, "#probe_2993");
    await selectOperationOption(fixture, 12, '#probeOperation'); // Selecting an invalid operation

    // Validate error message for failed operation
    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);

    // Attempt marking probe as RMA and validate success message
    await selectOperationOption(fixture, 10, '#probeOperation');
    expect(toastrServiceMock.success).toHaveBeenCalledWith("Probe is successfully marked as RMA.");

    // Attempt disabling the probe and validate success message
    await clickOnButtonOrCheckBox(fixture, "#probe_2993");
    await selectOperationOption(fixture, 9, '#probeOperation');
    expect(toastrServiceMock.success).toHaveBeenCalledWith("Probe is successfully marked as Disable.");

    // Attempt sales order association update and validate success message
    await clickOnButtonOrCheckBox(fixture, "#probe_2993");
    await selectOperationOption(fixture, 6, '#probeOperation');
    expect(toastrServiceMock.success).toHaveBeenCalledWith("Sales Order details are updated successfully.");

    // Attempt another operation requiring confirmation
    await clickOnButtonOrCheckBox(fixture, "#probe_2993");
    await selectOperationOption(fixture, 7, '#probeOperation');
    await conformDialog(fixture, '#confirmationOkButton');

    // Attempt updating probe type
    await clickOnButtonOrCheckBox(fixture, "#probe_2993");
    await selectOperationOption(fixture, 11, '#probeOperation');

    // Locate the probe type update component in the DOM
    const updateProbeTypeComponentElement = document.querySelector('app-update-probe-type');

    if (updateProbeTypeComponentElement && window['ng']) {
      // Retrieve the Angular component instance
      const updateProbeTypeComponent = window['ng'].getComponent(updateProbeTypeComponentElement);

      // Set a new value for the probe type form field
      updateProbeTypeComponent.probeForm.get('probeType').setValue(1);

      fixture.detectChanges();
      await fixture.whenStable();

      // Confirm the probe type update action
      await conformDialog(fixture, "#uploadBtn");

      // Validate success message for probe type update
      expect(toastrServiceMock.success).toHaveBeenCalledWith("Probe type updated successfully.");
    }

    // Verify probe selection is cleared at the end
    await clickOnButtonOrCheckBox(fixture, "#probe_2993");
    await clickOnButtonOrCheckBox(fixture, "#probe_2993");
    expect(component.probeIdList.length).toBe(0);
  });


  it('should handle probe filtering operations with valid inputs and trigger country change event', async () => {
    // Mock user permissions for the test
    mockPermission();

    // Mock API services for various probe-related operations
    mockProbeApiService();

    // Initialize the component and trigger change detection
    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    // Click on the search probe button without selecting filters
    await clickOnButtonOrCheckBox(fixture, "#searchProbeBtn");

    // Expect an info message prompting the user to select a filter
    expect(toastrServiceMock.info).toHaveBeenCalledWith(COMMON_SELECT_FILTER);

    // Click on the device historical data button
    await clickOnButtonOrCheckBox(fixture, "#deviceHistoricalData");

    // Set filter form values for probe search
    component.filterForm.get("salesOrderNumber").setValue(['00000125']); // Set Sales Order Number
    component.filterForm.get("serialNumber").setValue("123"); // Set Serial Number
    component.filterForm.get("probeTypes").setValue(['Torso1']); // Set Probe Type
    component.filterForm.get("probeFeatures").setValue([{ id: 3, displayName: 'Auto EF', isDisabled: false }]); // Set Probe Features
    component.filterForm.get("presetType").setValue([{ id: 1, displayName: 'Heart', isDisabled: false }]); // Set Preset Type
    component.filterForm.get("customerName").setValue("123"); // Set Customer Name
    component.filterForm.get("deviceModel").setValue("123"); // Set Device Model
    component.filterForm.get("manufacturer").setValue("123"); // Set Manufacturer
    component.filterForm.get("featureValidityPeriod").setValue([{ value: true, key: 'With expiry date' }]); // Set Feature Validity Period
    component.filterForm.get("osType").setValue([{ key: 'BRIDGE', value: 'Bridge' }]); // Set OS Type
    component.filterForm.get("productStatus").setValue([{ key: 'ENABLED', value: 'Enable' }]); // Set Product Status
    component.filterForm.get("deviceHistoricalData").setValue("history"); // Set Device Historical Data
    component.filterForm.get("countries").setValue([{ id: 73, country: 'Algeria' }]); // Set Country Filter
    component.filterForm.get("lockState").setValue([{ value: true, key: 'Locked' }]); // Set Lock State
    component.filterForm.get("probeEditState").setValue([{ value: true, key: 'Allowed' }]); // Set Probe Edit State

    // Click on the search probe button after setting filters
    await clickOnButtonOrCheckBox(fixture, "#searchProbeBtn");

    // Locate the country dropdown element
    const countryDropdown = fixture.debugElement.query(By.css('#probeCountry'));
    const dropdownComponent = countryDropdown.componentInstance;

    // Simulate selecting a country and trigger the country change event
    dropdownComponent.onSelect.emit({ id: -1, countryName: 'Test Country' });
    fixture.detectChanges();
    await fixture.whenStable();

    // Attempt updating probe type
    await clickOnButtonOrCheckBox(fixture, "#probe_2993");
    await selectOperationOption(fixture, 5, '#probeOperation');
    const okBtnActive = document.querySelector<HTMLElement>('#feature_1');
    if (okBtnActive) {
      // Simulate the button click for the updated state
      okBtnActive.click();

      fixture.detectChanges();
      await fixture.whenStable();
    }

    await conformDialog(fixture, '#feature_1');

    await conformDialog(fixture, '#isDownload');

    await conformDialog(fixture, '#isReminder');

  });

  it('should handle probe details visibility', async () => {
    // Mock user permissions for the test
    mockPermission();

    // Mock API services for various probe-related operations
    mockProbeApiService();

    // Initialize the component and trigger change detection
    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    clickOnButtonOrCheckBox(fixture, "#probeDetails");

    expect(component.displayOTSDetail).toBeTruthy();

  });

  it('should handle add probe functionality', async () => {
    // Mock user permissions and API services
    mockPermission();
    mockProbeApiService();
    salesOrderApiCallServiceSpy.getBasicSalesOrderDetails.and.returnValue(of(new HttpResponse<BasicSalesOrderDetailResponse>({
      body: new BasicSalesOrderDetailResponse("20250123", "mohit1", "<EMAIL>", 73, null, true, false, null),
      status: 200,
      statusText: 'OK',
    })));

    // Initialize component
    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    // Click the add probe button
    await clickOnButtonOrCheckBox(fixture, '#addProbeBtn');

    fixture.detectChanges();
    await fixture.whenStable();

    expect(component.displayOtsProbeAddUpdate).toBeTruthy();

    // Get the dropdown element
    const dropdown = fixture.debugElement.query(By.css('#salesOrderNumberField')).nativeElement;

    // Simulate a click event to open the dropdown
    dropdown.click();
    fixture.detectChanges();
    await fixture.whenStable();

    // Create and dispatch the custom event for onDeSelect
    const event = new CustomEvent('onDeSelect', { detail: { id: 1, name: 'Item 1' } });
    dropdown.dispatchEvent(event);

    fixture.detectChanges();
    await fixture.whenStable();

    // Dispatch the custom event for 'onSelect'
    const eventSelect = new CustomEvent('onSelect', { detail: { id: 1, name: 'Test Order' } });
    dropdown.dispatchEvent(eventSelect);

    fixture.detectChanges();
    await fixture.whenStable();

    clickOnButtonOrCheckBox(fixture, '#addSalesOrderBtn');

    clickOnButtonOrCheckBox(fixture, '#closeManualSalesOrderBtn');

    clickOnButtonOrCheckBox(fixture, '#addSalesOrderFormBtn');
  });


  it('should handle add probe functionality', async () => {
    // Mock user permissions and API services
    mockPermission();
    mockProbeApiService();
    salesOrderApiCallServiceSpy.getBasicSalesOrderDetails.and.returnValue(of(new HttpResponse<BasicSalesOrderDetailResponse>({
      body: new BasicSalesOrderDetailResponse("20250123", "mohit1", "<EMAIL>", 73, null, true, false, null),
      status: 200,
      statusText: 'OK',
    })));

    // Initialize component
    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    // Click the add probe button
    await clickOnButtonOrCheckBox(fixture, '#addProbeBtn');

    fixture.detectChanges();
    await fixture.whenStable();

    const addSalesOrderBtn = fixture.debugElement.query(By.css('#addSalesOrderBtn')).nativeElement;
    addSalesOrderBtn.click();

    fixture.detectChanges();
    await fixture.whenStable();

    const closeManualSalesOrderBtn = fixture.debugElement.query(By.css('#closeManualSalesOrderBtn')).nativeElement;
    closeManualSalesOrderBtn.click();

    fixture.detectChanges();
    await fixture.whenStable();

    const addSalesOrderFormBtn = fixture.debugElement.query(By.css('#addSalesOrderFormBtn')).nativeElement;
    addSalesOrderFormBtn.click();

    fixture.detectChanges();
    await fixture.whenStable();

    const operation = fixture.nativeElement.querySelector('#probeTypeForNewProbe');
    if (operation) {
      operation.value = operation.options[0]?.value;
      operation.dispatchEvent(new Event('change'));
    }

    fixture.detectChanges();
    await fixture.whenStable();


    const inputEl = fixture.debugElement.query(By.css('#createProbeSerialNumber'));

    // Simulate typing "ABC123" in the input
    const value = 'ABC123';
    inputEl.triggerEventHandler('input', { target: { value } });

    fixture.detectChanges();
    await fixture.whenStable();

    const checkbox = fixture.debugElement.query(By.css(`#Heart_1`)).nativeElement;

    // Simulate change event
    checkbox.dispatchEvent(new Event('change', { bubbles: true }));

    fixture.detectChanges();
    await fixture.whenStable();


    const perpetualCheckbox = fixture.debugElement.query(By.css(`#Perpetual_Heart_1`)).nativeElement;

    perpetualCheckbox.dispatchEvent(new Event('change', { bubbles: true }));
    fixture.detectChanges();
    await fixture.whenStable();

    const trio_6 = fixture.debugElement.query(By.css('#Trio_6')).nativeElement;

    trio_6.dispatchEvent(new Event('change', { bubbles: true }));

    fixture.detectChanges();
    await fixture.whenStable();

    const perpetualTrioCheckbox = fixture.debugElement.query(By.css('#Perpetual_Trio_6')).nativeElement;

    perpetualTrioCheckbox.dispatchEvent(new Event('change', { bubbles: true }));

    fixture.detectChanges();
    await fixture.whenStable();

    const salesOrderNumberField = fixture.debugElement.query(By.css('#salesOrderNumberField')).nativeElement;

    salesOrderNumberField.dispatchEvent(new Event('onSelect', { bubbles: true }));

    fixture.detectChanges();
    await fixture.whenStable();

    salesOrderNumberField.dispatchEvent(new Event('onDeSelect', { bubbles: true }));

    fixture.detectChanges();
    await fixture.whenStable();

    salesOrderNumberField.dispatchEvent(new Event('click', { bubbles: true }));

    fixture.detectChanges();
    await fixture.whenStable();

    const addButton = fixture.debugElement.query(By.css('#addSingleProbeBtn'));
    addButton.triggerEventHandler('click', null);

    fixture.detectChanges();
    await fixture.whenStable();

    const editSingleProbeBtn = fixture.debugElement.query(By.css('#editSingleProbeBtn'));
    editSingleProbeBtn.triggerEventHandler('click', null);

    fixture.detectChanges();
    await fixture.whenStable();

    const updateEditedProbeBtn = fixture.debugElement.query(By.css('#updateEditedProbeBtn'));
    updateEditedProbeBtn.triggerEventHandler('click', null);

    fixture.detectChanges();
    await fixture.whenStable();

    const deleteSingleProbeBtn = fixture.debugElement.query(By.css('#deleteSingleProbeBtn'));
    deleteSingleProbeBtn.triggerEventHandler('click', null);

  });

});

