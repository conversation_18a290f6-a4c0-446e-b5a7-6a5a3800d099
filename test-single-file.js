const { execSync } = require('child_process');

try {
  console.log('Running OtsProbesDetailComponent tests...');
  
  // Try to run just the specific test file
  const result = execSync('npx ng test --include="**/ots-probes-detail.component.spec.ts" --watch=false --browsers=ChromeHeadless', {
    cwd: process.cwd(),
    stdio: 'inherit',
    timeout: 120000
  });
  
  console.log('Tests completed successfully!');
} catch (error) {
  console.error('Test execution failed:', error.message);
  process.exit(1);
}
