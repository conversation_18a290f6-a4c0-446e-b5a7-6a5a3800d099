import { HttpResponse } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { AuditDetailResponse } from 'src/app/model/Audit/auditDetailResponse.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { AuditApiCallService } from 'src/app/shared/Service/Audit/audit-api-call.service';
import { commonsProviders, mockError, testErrorHandling } from 'src/app/Tesing-Helper/test-utils';
import { AuditDetailModelComponent } from './audit-detail-model.component';
import { SuccessMessageResponse } from 'src/app/model/common/SuccessMessageResponse.model';
import { INTERNAL_SERVER_ERROR } from 'src/app/app.constants';
import { PermissionService } from 'src/app/shared/permission.service';
import { EnumMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';

describe('AuditDetailModelComponent', () => {
    let component: AuditDetailModelComponent;
    let fixture: ComponentFixture<AuditDetailModelComponent>;
    let toastrServiceMock: jasmine.SpyObj<ToastrService>;
    let auditApiCallServiceMock: jasmine.SpyObj<AuditApiCallService>;
    let exceptionHandlingServiceMock: ExceptionHandlingService;
    let localStorageServiceMock: jasmine.SpyObj<LocalStorageService>;
    let confirmDialogServiceMock: jasmine.SpyObj<ConfirmDialogService>;


    beforeEach(async () => {
        toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
        auditApiCallServiceMock = jasmine.createSpyObj('AuditApiCallService', ['getAuditDetail', 'reverseTransferProduct']);
        localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve', 'store', 'clear']);
        confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['confirm']);

        await TestBed.configureTestingModule({
            declarations: [AuditDetailModelComponent],
            imports: [],
            providers: [
                NgbActiveModal,
                SessionStorageService,
                HidePermissionNamePipe,
                PermissionService,
                PrintListPipe,
                EnumMappingDisplayNamePipe,
                LocalStorageService,
                { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
                { provide: AuditApiCallService, useValue: auditApiCallServiceMock },
                { provide: LocalStorageService, useValue: localStorageServiceMock },
                commonsProviders(toastrServiceMock)
            ]
        })
            .compileComponents();
        fixture = TestBed.createComponent(AuditDetailModelComponent);
        exceptionHandlingServiceMock = TestBed.inject(ExceptionHandlingService);
        localStorageServiceMock.retrieve.and.returnValue({ name: 'test user' });
        component = fixture.componentInstance;
        confirmDialogServiceMock.confirm.and.returnValue(Promise.resolve(true));
        fixture.detectChanges();
    });
    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should call getAuditDetail API Error', () => {
        auditApiCallServiceMock.getAuditDetail.and.returnValue(of(new HttpResponse<Array<AuditDetailResponse>>({
            body: null,
            status: 204,
            statusText: 'No Content',
        })));
        component.ngOnInit();
        fixture.detectChanges();

        expect(toastrServiceMock.info).toHaveBeenCalledWith('No Data');
    });

    it('should call toastrService.error with INTERNAL_SERVER_ERROR', async () => {
        await testErrorHandling(auditApiCallServiceMock.getAuditDetail, () => component.ngOnInit(), exceptionHandlingServiceMock, toastrServiceMock, fixture);
    });

    it('should call ReverseTransfer API', async () => {
        auditApiCallServiceMock.reverseTransferProduct.and.returnValue(of(new HttpResponse<SuccessMessageResponse>({
            body: { message: 'Product Reversed Successfully' },
            status: 200,
            statusText: 'OK',
        })));
        component.ngOnInit();
        fixture.detectChanges();

        component.reverseTransferProduct();
        fixture.detectChanges();
        await fixture.whenStable()

        expect(toastrServiceMock.success).toHaveBeenCalledWith('Product Reversed Successfully');
    });

    it('should call toastrService.error with INTERNAL_SERVER_ERROR', async () => {
        auditApiCallServiceMock.reverseTransferProduct.and.returnValue(throwError(() => mockError));
        component.ngOnInit();
        fixture.detectChanges();

        component.reverseTransferProduct();
        fixture.detectChanges();
        await fixture.whenStable()

        expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);
    });

    it('should call reverseTransferProduct API Error', async () => {
        auditApiCallServiceMock.reverseTransferProduct.and.returnValue(of(new HttpResponse<SuccessMessageResponse>({
            body: null,
            status: 204,
            statusText: 'OK',
        })));
        component.ngOnInit();
        fixture.detectChanges();

        component.reverseTransferProduct();
        fixture.detectChanges();
        await fixture.whenStable()

        expect(toastrServiceMock.error).toHaveBeenCalled();
    });
})