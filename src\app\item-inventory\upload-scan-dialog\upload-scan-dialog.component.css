@import '../../../assets/css/custom_style.css';

#file {
    width: 90%;
}

.upload-form-control {
    display: block;
    width: 100%;
    height: calc(1.5em + .75rem + 2px);
    padding: .375rem .75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    /* border: 1px solid #ced4da; */
    border: 1px solid #adadad;
    border-radius: .25rem;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}

.upload-form-control:focus {
    color: #495057;
    background-color: #fff;
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgb(0 123 255 / 25%);
}

.upload-title {
    color: #495057;
    font-weight: bold;
    white-space: nowrap;
    padding-right: 10px
}

.upload-success {
    vertical-align: middle;
    color: #6bad47;
    font-weight: 400;
}

#uploadScanDialog table {
    width: 100%;
}

#uploadScanDialog .validation {
    color: #c70e24;
    font-size: 12px;
}

 #uploadScanDialog .upload-container {
    width: 100%;
    text-align: center;
    padding:0 20px;
  }
  
  #uploadScanDialog  .upload-container h3 {
    font-size: 16px;
    color: #333;
  }
  
  #uploadScanDialog .progress-bar-wrapper {
    width: 100%;
    height: 20px;
    background-color: #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
  }
  
  #uploadScanDialog  .progress-bar {
    height: 100%;
    background-color: rgb(17, 171, 17);
    width: 0;
    transition: width 0.4s ease; /* Smooth progress transition */
  }
  
  #uploadScanDialog  .progress-details {
    font-size: 12px;
    color: #555;
  }

  #uploadScanDialog .text-info{
    padding: 0;
    margin: 0;
    text-align: start;
    font-size: small;
  }
  