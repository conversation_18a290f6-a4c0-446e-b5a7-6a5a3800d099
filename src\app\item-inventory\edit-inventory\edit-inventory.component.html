<!-- model header start -->
<div class="modal-header">
    <!-- model title start -->
    <label class="modal-title">{{ basicModelConfig?.title }}</label>
    <!-- model title end -->
</div>
<!-- model header end -->
<!-- model body start -->
<div class="modal-body" id="editInventoryDialog">
    <div>
        <!-- upload form start -->
        <form enctype="multipart/form-data" name='fileinfo' [formGroup]="form">
            <!-- table start -->
            <table aria-hidden="true" class="w-100">
                <!-- title row start -->
                <tr>
                    <!-- title header start -->
                    <th class="upload-title">
                        <span>Title</span>
                    </th>
                    <!-- title header end -->
                    <td>
                        <!-- title input start -->
                        <input type="text" class="upload-form-control disabledTextBox" id="title" name="title"
                            formControlName="Title" tabindex="-1" [readonly]="textFieldDisabled">
                        <!-- validation for partNumber start -->
                        <div
                            *ngIf="(form.get('Title').touched || form.get('Title').dirty) && form.get('Title').invalid">
                            <div *ngIf="form.get('Title').errors['required']">
                                <span class="validation">Title is required</span>
                            </div>
                            <div *ngIf="form.get('Title').errors['maxlength']">
                                <span class="validation">{{textBoxMaxCharactersAllowedMessage}}</span>
                            </div>
                        </div>
                        <!-- validation for partNumber end -->
                    </td>
                </tr>
                <!-- title row end -->

                <!-- device type row start -->
                <tr>
                    <!-- Device Type header start -->
                    <th class="upload-title">
                        <span>Device Type</span>
                    </th>
                    <!-- Device Type header end -->
                    <td>
                        <!-- multiselect dropdown for Device Type start -->
                        <ng-multiselect-dropdown name="deviceTypes" [placeholder]="''" class="devicePageDeviceType"
                            formControlName="deviceTypes" [settings]="dropdownSettingsDeviceTypes" [data]="deviceTypes">
                        </ng-multiselect-dropdown>
                        <!-- multiselect dropdown for Device Type end -->
                    </td>
                </tr>
                <!-- device type row end -->

                <!-- inventory status row start -->
                <tr>
                    <!-- Inventory Status header start -->
                    <th class="upload-title">
                        <span>Status</span>
                    </th>
                    <!-- Inventory Status header end -->
                    <td>
                        <!-- multiselect dropdown for Inventory Status start -->
                        <ng-multiselect-dropdown class="devicePageDeviceType" name="inventoryStatus" [placeholder]="''"
                            formControlName="inventoryStatus" [settings]="dropdownSettingsSoftwareStatus"
                            [data]="inventoryStatus">
                        </ng-multiselect-dropdown>
                        <!-- multiselect dropdown for Inventory Status end -->
                        <!-- validation for partNumber start -->
                        <div
                            *ngIf="(form.get('inventoryStatus').touched || form.get('inventoryStatus').dirty) && form.get('inventoryStatus').invalid ">
                            <div *ngIf="form.get('inventoryStatus').errors['required']">
                                <span class="validation">Status is required</span>
                            </div>
                        </div>
                        <!-- validation for partNumber end -->

                    </td>
                </tr>
                <!-- inventory status row end -->

                <!-- video json version row start -->
                <tr>
                    <!-- video json version header start -->
                    <th class="upload-title">
                        <span>Json Version</span>
                    </th>
                    <!-- video json version header end -->
                    <td>
                        <!-- multiselect dropdown for video json version start -->
                        <ng-multiselect-dropdown class="devicePageDeviceType" name="jsonVersion" [placeholder]="''"
                            formControlName="jsonVersion" [settings]="dropdownSettingsJsonVersion"
                            [data]="jsonVersionList">
                        </ng-multiselect-dropdown>
                        <!-- multiselect dropdown for video json version end -->
                    </td>
                </tr>
                <!-- video json version row end -->

                <!----------------------------------------->
                <!------------Country----------->
                <!----------------------------------------->
                <tr>
                    <th class="upload-title">
                        <span>Country</span>
                    </th>
                    <td>
                        <!-- multiselect dropdown for Country start -->
                        <ng-multiselect-dropdown name="Country" [placeholder]="''" formControlName="country"
                            [settings]="countryDropdownSettings" [data]="countryList">
                        </ng-multiselect-dropdown>
                        <!-- multiselect dropdown for Country end -->
                        <!-- validation for partNumber start -->
                        <div
                            *ngIf="(form.get('country').touched || form.get('country').dirty) && form.get('country').invalid ">
                            <div *ngIf="form.get('country').errors['required']">
                                <span class="validation">Country is required</span>
                            </div>
                        </div>
                        <!-- validation for partNumber end -->
                    </td>
                </tr>
                <!----------------------------------------->
                <!----------------------------------------->

                <!----------------------------------------->
                <!----------------------------------------->
                <!----------------------------------------->
                <tr>
                    <th class="upload-title">
                        <span>Part Number</span>
                    </th>
                    <td>
                        <!-- title input start -->
                        <input type="text" class="upload-form-control" id="partNumber" name="partNumber"
                            formControlName="partNumber">
                        <!-- validation for partNumber start -->
                        <div
                            *ngIf="(form.get('partNumber').touched || form.get('partNumber').dirty) && form.get('partNumber').invalid ">
                            <div *ngIf="form.get('partNumber').errors['required']">
                                <span class="validation">Part Number is required</span>
                            </div>
                            <div *ngIf="form.get('partNumber').errors['maxlength']">
                                <span class="validation">{{small_textBoxMaxCharactersAllowedMessage}}</span>
                            </div>
                        </div>
                        <!-- validation for partNumber end -->
                        <!-- validation for partNumber end -->
                    </td>
                </tr>
                <!----------------------------------------->
                <!----------------------------------------->
            </table>
            <!-- table end -->
        </form>
        <!-- upload form end -->
    </div>
</div>
<!-- model body end -->

<hr class="pb-2" />
<div class=" d-flex align-items-center justify-content-end">
    <div class="d-flex mb-1">
        <button type="button" tabindex="-1" class="btn btn-sm btn-orange mx-1" id="editInventoryCancel"
            (click)="decline()">{{
            basicModelConfig?.btnCancelText }}</button>
        <button type="button" class="btn btn-sm btn-orange" id="uploadBtn" (click)="accept()"
            [disabled]="form.invalid">{{
            basicModelConfig?.btnOkText }}</button>
    </div>
</div>