import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { ConfigInjectService } from '../shared/InjectService/config-inject.service';
import { AuthJwtService } from '../shared/auth-jwt.service';
import { ActivatedRoute, Router } from '@angular/router';
import { isNullOrUndefined } from 'is-what';
import { ConfirmDialogService } from '../confirmationdialog/confirmation.service';
import { LoginResponse } from '../model/Login/LoginResponse.model';
import { SSO_FAILED_MESSAGE, SSO_FAILED_TITLE } from '../app.constants';
import { SSOLoginService } from '../shared/Service/SSO/ssologin.service';


@Component({
  selector: 'app-sso-login',
  templateUrl: './sso-login.component.html',
  styleUrl: './sso-login.component.css',
  encapsulation: ViewEncapsulation.None
})
export class SsoLoginComponent implements OnInit {

  loading: boolean = false;
  rdmVersion: string;
  isLoginPopupDisplay: boolean = true;

  constructor(
    private router: Router,
    private authservice: AuthJwtService,
    private configInjectService: ConfigInjectService,
    private route: ActivatedRoute,
    private confirmDialogService: ConfirmDialogService,
    private ssoLoginService: SSOLoginService
  ) {
    this.rdmVersion = this.configInjectService.getRDMVersion();
  }

  public ngOnInit(): void {
    let queryparamsResponse = this.getQueryparams();
    if (!this.authservice.isAuthenticate()) {
      if (!isNullOrUndefined(queryparamsResponse) && queryparamsResponse == "success") {
        this.isLoginPopupDisplay = false;
        this.synchronize();
      } else if (!isNullOrUndefined(queryparamsResponse) && queryparamsResponse == "failed") {
        this.isLoginPopupDisplay = true;
        this.confirmDialogService.confirm(SSO_FAILED_TITLE, SSO_FAILED_MESSAGE)
          .then((confirmed: boolean) => { }).finally(() => {
            this.ssoLoginService.logOutInMicrosoft();
          });
      } else {
        this.isLoginPopupDisplay = true;
      }
    } else {
      this.router.navigate(['modules']);
    }
  }

  private getQueryparams(): string {
    let queryparamsResponse = null;
    this.route.queryParams.subscribe((params) => {
      queryparamsResponse = params['login'];
    });
    return queryparamsResponse;
  }

  public synchronize(): void {
    this.loading = true;
    this.authservice.syncUserInfo()?.subscribe({
      next: (response: HttpResponse<LoginResponse>) => {
        this.authservice.setPermission(response.body.permissions);
        this.authservice.setUserId(response.body.userId);
        this.authservice.setCountryMasters(response.body.countryMasters);
        this.authservice.setUserRoles(response.body.authorities);
        this.router.navigate(['modules']);
        this.loading = false;
      },
      error: (error: HttpErrorResponse) => {
        this.loading = false;
      }
    });
  }

  public loginWithMicrosoft(): void {
    this.ssoLoginService.loginWithMicrosoft();
  }
}
