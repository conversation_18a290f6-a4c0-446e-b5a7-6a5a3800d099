import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { SsoLoginComponent } from './sso-login.component';
import { AuthJwtService } from '../shared/auth-jwt.service';
import { ConfirmDialogService } from '../confirmationdialog/confirmation.service';
import { SSOLoginService } from '../shared/Service/SSO/ssologin.service';
import { ConfigInjectService } from '../shared/InjectService/config-inject.service';
import { ActivatedRoute, Router } from '@angular/router';
import { of, throwError } from 'rxjs';
import { HttpResponse } from '@angular/common/http';
import { LoginResponse } from '../model/Login/LoginResponse.model';

describe('SsoLoginComponent Full Coverage', () => {
  let component: SsoLoginComponent;
  let fixture: ComponentFixture<SsoLoginComponent>;
  let authServiceMock: any;
  let confirmDialogServiceMock: any;
  let ssoLoginServiceMock: any;
  let configInjectServiceMock: any;
  let activatedRouteMock: any;
  let routerMock: any;

  beforeEach(async () => {
    authServiceMock = {
      isAuthenticate: jasmine.createSpy(),
      syncUserInfo: jasmine.createSpy(),
      setPermission: jasmine.createSpy(),
      setUserId: jasmine.createSpy(),
      setCountryMasters: jasmine.createSpy(),
      setUserRoles: jasmine.createSpy()
    };

    confirmDialogServiceMock = {
      confirm: jasmine.createSpy().and.returnValue(Promise.resolve(true))
    };

    ssoLoginServiceMock = {
      loginWithMicrosoft: jasmine.createSpy(),
      logOutInMicrosoft: jasmine.createSpy()
    };

    configInjectServiceMock = {
      getRDMVersion: jasmine.createSpy().and.returnValue('1.0.0')
    };

    routerMock = {
      navigate: jasmine.createSpy()
    };

    activatedRouteMock = {
      queryParams: of({ login: 'success' })
    };

    await TestBed.configureTestingModule({
      declarations: [SsoLoginComponent],
      providers: [
        { provide: AuthJwtService, useValue: authServiceMock },
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        { provide: SSOLoginService, useValue: ssoLoginServiceMock },
        { provide: ConfigInjectService, useValue: configInjectServiceMock },
        { provide: ActivatedRoute, useValue: activatedRouteMock },
        { provide: Router, useValue: routerMock }
      ]
    }).compileComponents();
  });

  function initComponentWithQueryParam(param: string, isAuthenticated: boolean) {
    activatedRouteMock.queryParams = of({ login: param });
    authServiceMock.isAuthenticate.and.returnValue(isAuthenticated);
    fixture = TestBed.createComponent(SsoLoginComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  }

  it('should create the component', () => {
    initComponentWithQueryParam('success', false);
    expect(component).toBeTruthy();
    expect(component.rdmVersion).toBe('1.0.0');
  });

  it('should navigate if already authenticated', () => {
    initComponentWithQueryParam('success', true);
  });

  it('should call synchronize if query param is success and user not authenticated', fakeAsync(() => {
    const mockResponse = new HttpResponse<LoginResponse>({
      body: {
        permissions: ['READ'],
        userId: 1,
        countryMasters: [],
        authorities: ['ADMIN'],
        login: 'testuser',
        email: '<EMAIL>'
      }
    });

    authServiceMock.isAuthenticate.and.returnValue(false);
    authServiceMock.syncUserInfo.and.returnValue(of(mockResponse));
    initComponentWithQueryParam('success', false);
    tick();

    expect(authServiceMock.syncUserInfo).toHaveBeenCalled();
  }));

  it('should handle error in synchronize()', fakeAsync(() => {
    authServiceMock.syncUserInfo.and.returnValue(throwError(() => new Error('Failed')));
    initComponentWithQueryParam('success', false);
    tick();

    expect(authServiceMock.syncUserInfo).toHaveBeenCalled();
    expect(component.loading).toBeFalse();
  }));

  it('should show login popup and show error dialog on failed login', fakeAsync(() => {
    initComponentWithQueryParam('failed', false);
    tick();

    expect(confirmDialogServiceMock.confirm).toHaveBeenCalled();
    expect(ssoLoginServiceMock.logOutInMicrosoft).toHaveBeenCalled();
    expect(component.isLoginPopupDisplay).toBeTrue();
  }));

  it('should show login popup when query param is not success or failed', () => {
    initComponentWithQueryParam(undefined as any, false);
    expect(component.isLoginPopupDisplay).toBeTrue();
  });

  it('should trigger Microsoft login method', () => {
    initComponentWithQueryParam(undefined as any, false);
    component.loginWithMicrosoft();
    expect(ssoLoginServiceMock.loginWithMicrosoft).toHaveBeenCalled();
  });
});
