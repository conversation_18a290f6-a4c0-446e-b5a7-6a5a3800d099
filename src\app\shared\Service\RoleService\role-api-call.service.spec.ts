import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { HttpTestingController } from '@angular/common/http/testing';
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import { ToastrService } from 'ngx-toastr';
import { of, throwError } from 'rxjs';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { RoleReqeest } from 'src/app/model/Role/roleRequest.model';
import { BASE_URL, commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { ExceptionHandlingService } from '../../ExceptionHandling.service';
import { ConfigInjectService } from '../../InjectService/config-inject.service';
import { PermissionService } from '../../permission.service';
import { HidePermissionNamePipe } from '../../pipes/Role/hidePermissionName.pipe';
import { CommonsService } from '../../util/commons.service';
import { RoleApiCallService } from './role-api-call.service';
import { RoleService } from './role.service';

describe('RoleApiCallService', () => {
  let service: RoleApiCallService;
  let httpMock: HttpTestingController;
  let toastr: jasmine.SpyObj<ToastrService>;
  let roleService: jasmine.SpyObj<RoleService>;
  let permissionService: jasmine.SpyObj<PermissionService>;
  let exceptionService: jasmine.SpyObj<ExceptionHandlingService>;
  let hidePermissionPipe: jasmine.SpyObj<HidePermissionNamePipe>;
  let confirmDialogService: jasmine.SpyObj<ConfirmDialogService>;
  let commonsService: jasmine.SpyObj<CommonsService>;

  beforeEach(() => {
    toastr = jasmine.createSpyObj('ToastrService', ['success', 'info']);
    roleService = jasmine.createSpyObj('RoleService', ['isLoading', 'callRefreshPageSubject']);
    permissionService = jasmine.createSpyObj('PermissionService', ['getPermissionForPermissionList', 'getRolePermission']);
    exceptionService = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);
    hidePermissionPipe = jasmine.createSpyObj('HidePermissionNamePipe', ['transform']);
    confirmDialogService = jasmine.createSpyObj('ConfirmDialogService', ['confirm']);
    commonsService = jasmine.createSpyObj('CommonsService', ['handleError']);

    TestBed.configureTestingModule({
      imports: [],
      providers: [
        RoleApiCallService,
        { provide: RoleService, useValue: roleService },
        { provide: PermissionService, useValue: permissionService },
        { provide: ExceptionHandlingService, useValue: exceptionService },
        { provide: HidePermissionNamePipe, useValue: hidePermissionPipe },
        ConfigInjectService,
        { provide: ConfirmDialogService, useValue: confirmDialogService },
        { provide: CommonsService, useValue: commonsService },
        commonsProviders(toastr),
      ]
    });

    service = TestBed.inject(RoleApiCallService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify(); // Ensure no unexpected requests are pending
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should get role permission list with permission', async () => {
    const mockPermissions = null;

    permissionService.getPermissionForPermissionList.and.returnValue(true);

    const resultPromise = service.getRolePermissionList('testResource');

    const req = httpMock.expectOne(`${BASE_URL}api/permission`);
    expect(req.request.method).toBe('GET');

    req.flush(mockPermissions); // simulate HTTP response

    const result = await resultPromise;
    expect(result).toBeUndefined();
  });

  it('should handle error in getRolePermissionList', async () => {
    permissionService.getPermissionForPermissionList.and.returnValue(true);
    const errorResponse = new HttpErrorResponse({ error: 'err', status: 500 });
    spyOn(service['http'], 'get').and.returnValue(throwError(() => errorResponse));

    const result = await service.getRolePermissionList('resource');

    expect(result).toEqual([]);
    expect(exceptionService.customErrorMessage).toHaveBeenCalled();
  });

  it('should return empty if permission denied in getRolePermissionList', async () => {
    permissionService.getPermissionForPermissionList.and.returnValue(false);
    const result = await service.getRolePermissionList('resource');
    expect(result).toEqual([]);
  });

  it('should create role', () => {
    const request: RoleReqeest = { roleName: 'Admin' } as any;
    service.createRole(request).subscribe(res => {
      expect(res.body.message).toEqual('Success');
    });

    const req = httpMock.expectOne(`${BASE_URL}api/role`);
    req.flush({ message: 'Success' }, { status: 200, statusText: 'OK' });
  });

  it('should update role', () => {
    const request: RoleReqeest = { roleName: 'UpdatedRole' } as any;
    service.updateRole(1, request).subscribe(res => {
      expect(res.status).toBe(200);
    });

    const req = httpMock.expectOne(`${BASE_URL}api/role/1`);
    req.flush({}, { status: 200, statusText: 'OK' });
  });

  it('should delete role', () => {
    service.deleteRole([1]).subscribe(res => {
      expect(res.status).toBe(200);
    });

    const req = httpMock.expectOne(`${BASE_URL}api/role/1`);
    req.flush({}, { status: 200, statusText: 'OK' });
  });

  it('should get role detail', () => {
    service.getRoleDetail(1).subscribe(res => {
      expect(res.status).toBe(200);
    });

    const req = httpMock.expectOne(`${BASE_URL}api/role/1`);
    req.flush({}, { status: 200, statusText: 'OK' });
  });

  it('should get role name list with permission', async () => {
    permissionService.getRolePermission.and.returnValue(true);
    spyOn(service['http'], 'get').and.returnValue(of({ body: ['Admin'] }));

    const result = await service.getRoleNameList();
    expect(result).toEqual(['Admin']);
  });

  it('should return empty role name list if no permission', async () => {
    permissionService.getRolePermission.and.returnValue(false);
    const result = await service.getRoleNameList();
    expect(result).toEqual([]);
  });

  it('should handle error in getRoleNameList', async () => {
    permissionService.getRolePermission.and.returnValue(true);
    const errorResponse = new HttpErrorResponse({ error: 'err', status: 500 });
    spyOn(service['http'], 'get').and.returnValue(throwError(() => errorResponse));
    const result = await service.getRoleNameList();
    expect(result).toEqual([]);
  });

  it('should handle success in deleteRoleAction', () => {
    spyOn(service, 'deleteRole').and.returnValue(of(new HttpResponse({ body: { message: 'Deleted' }, status: 200 })));
    service['roleModifySuccessAfterAction'] = jasmine.createSpy();
    service.deleteRoleAction([1], 'res', true);
    expect(service.roleModifySuccessAfterAction).toHaveBeenCalled();
  });

  it('should handle error in deleteRoleAction', () => {
    const error = new HttpErrorResponse({ status: 500 });
    spyOn(service, 'deleteRole').and.returnValue(throwError(() => error));
    service['roleModifyErrorAfterAction'] = jasmine.createSpy();
    service.deleteRoleAction([1], 'res', true);
    expect(service.roleModifyErrorAfterAction).toHaveBeenCalled();
  });

  it('should confirm deleteRoleConfirmation and proceed', fakeAsync(() => {
    spyOn(service, 'deleteRoleAction');
    confirmDialogService.confirm.and.returnValue(Promise.resolve(true));
    service.deleteRoleConfirmation([1], 'res', true);
    tick();
    expect(service.deleteRoleAction).toHaveBeenCalledWith([1], 'res', true);
  }));

  it('should skip deleteRoleConfirmation if user cancels', fakeAsync(() => {
    spyOn(service, 'deleteRoleAction');
    confirmDialogService.confirm.and.returnValue(Promise.resolve(false));
    service.deleteRoleConfirmation([1], 'res', true);
    tick();
    expect(service.deleteRoleAction).not.toHaveBeenCalled();
  }));

  it('should show success on modify success', () => {
    const mockParam = {} as any;
    service.roleModifySuccessAfterAction(mockParam, 'Success', 'res', true);
    expect(toastr.success).toHaveBeenCalledWith('Success');
    expect(roleService.callRefreshPageSubject).toHaveBeenCalled();
  });

  it('should show info on modify error with status 412', () => {
    const error = new HttpErrorResponse({ status: 412 });
    service.roleModifyErrorAfterAction('res', error);
    expect(toastr.info).toHaveBeenCalled();
  });

  it('should handle modify error with other status', () => {
    const error = new HttpErrorResponse({ status: 500 });
    service.roleModifyErrorAfterAction('res', error);
    expect(exceptionService.customErrorMessage).toHaveBeenCalled();
  });
});
