import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { RoleApiCallService } from 'src/app/shared/Service/RoleService/role-api-call.service';
import { RoleService } from 'src/app/shared/Service/RoleService/role.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { RolefilterComponent } from './rolefilter.component';
import { EnumMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';

describe('RolefilterComponent', () => {
  let component: RolefilterComponent;
  let fixture: ComponentFixture<RolefilterComponent>;
  let localStorageServiceMock: jasmine.SpyObj<LocalStorageService>;


  beforeEach(async () => {
    const confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['open', 'close']);
    const toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);

    localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);

    // Mock the return value of retrieve for the getPermissionObject function
    localStorageServiceMock.retrieve.and.returnValue('mockedPermissionObject');
    await TestBed.configureTestingModule({
      declarations: [RolefilterComponent],
      imports: [NgMultiSelectDropDownModule.forRoot(), ReactiveFormsModule, FormsModule],
      providers: [
        RoleService,
        CommonsService,
        RoleApiCallService,
        ExceptionHandlingService,
        EnumMappingDisplayNamePipe,
        PrintListPipe,
        SessionStorageService,
        HidePermissionNamePipe,
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock }, // Provide mock ConfirmDialogService
        { provide: LocalStorageService, useValue: localStorageServiceMock }, // Provide mock LocalStorageService
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(RolefilterComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
