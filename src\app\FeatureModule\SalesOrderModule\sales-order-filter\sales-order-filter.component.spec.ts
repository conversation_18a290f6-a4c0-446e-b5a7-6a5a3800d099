import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SalesOrderFilterComponent } from './sales-order-filter.component';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { ToastrService } from 'ngx-toastr';
import { RoleApiCallService } from 'src/app/shared/Service/RoleService/role-api-call.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ReactiveFormsModule } from '@angular/forms';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { EnumMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';

describe('SalesOrderFilterComponent', () => {
  let component: SalesOrderFilterComponent;
  let fixture: ComponentFixture<SalesOrderFilterComponent>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);

    await TestBed.configureTestingModule({
      declarations: [SalesOrderFilterComponent],
      imports: [NgMultiSelectDropDownModule.forRoot(), ReactiveFormsModule],
      providers: [
        CommonsService,
        CommonOperationsService,
        RoleApiCallService,
        SessionStorageService,
        ExceptionHandlingService,
        EnumMappingDisplayNamePipe,
        PrintListPipe,
        HidePermissionNamePipe,
        LocalStorageService,
        ConfirmDialogService,
        commonsProviders(toastrServiceMock),
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(SalesOrderFilterComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
