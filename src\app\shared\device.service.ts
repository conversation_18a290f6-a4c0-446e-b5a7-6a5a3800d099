import { HttpClient, HttpResponse } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { createRequestOption } from '../../app/shared/util/request-util';
import { ProbDetailResource, probDownloadBodyButtonText, probDownloadCancelButtonText, probDownloadOkButtonText, probDownloadTitleButtonText } from '../app.constants';
import { ConfirmDialogService } from '../confirmationdialog/confirmation.service';
import { BasicSalesOrderDetailResponse } from '../model/SalesOrder/BasicSalesOrderDetailResponse.model';
import { SuccessMessageResponse } from '../model/common/SuccessMessageResponse.model';
import { IDevice } from '../model/device.model';
import { AssignSelectedReleaseVersionRequest } from '../model/device/AssignSelectedReleaseVersionRequest.model';
import { DeviceDetailResponse } from '../model/device/deviceDetailResponse.model';
import { DeviceSearchRequest } from '../model/device/deviceSearchRequest.model';
import { API_BASE_URL } from './config';
import { CommonsService } from './util/commons.service';
import { DownloadService } from './util/download.service';


@Injectable({
  providedIn: 'root'
})
export class DeviceService {
  public resourceUrl = this.SERVER_API_URL + 'api/deviceMasters';
  public inventoryUrl = this.SERVER_API_URL + 'api/inventory/';
  public featureUrl = this.SERVER_API_URL + 'api/features';

  constructor(protected http: HttpClient, @Inject(API_BASE_URL) public SERVER_API_URL: string,
    private downloadService: DownloadService,
    private confirmDialogService: ConfirmDialogService,
    private commonsService: CommonsService) { }

  getDeviceList(filterData: DeviceSearchRequest, req?: any): Observable<HttpResponse<IDevice[]>> {
    const options = createRequestOption(req);
    return this.http.post<IDevice[]>(this.resourceUrl + '/search', filterData, { params: options, observe: 'response' });
  }

  getpackageVersion(): Observable<HttpResponse<Array<string>>> {
    return this.http.get<Array<string>>(this.resourceUrl + '/packageVersion', { observe: 'response' });
  }

  public dowloadSasUriofFeatureLicenseConfirmationModel(resource: string): void {
    this.confirmDialogService.confirm(probDownloadTitleButtonText, probDownloadBodyButtonText, probDownloadOkButtonText, probDownloadCancelButtonText).then((res) => {
      if (resource == ProbDetailResource) {
        this.downloadService.subscribedownloadZipFileForProbDetailPageSubject(res);
      } else {
        this.downloadService.subscribeDownloadZipFileForProbSubject(res);
      }
    });
  }

  /**
   * Update Device Type To client
   * 
   * <AUTHOR>
   * @param listOfId 
   * @returns 
   */
  public updateDeviceTypeToClient(listOfId: any): Observable<HttpResponse<any>> {
    return this.http.put<any>(this.resourceUrl + '/type/client/' + listOfId, {}, { observe: 'response' });
  }

  /**
   * Update Device Type To demo
   * 
   * <AUTHOR>
   * @param listOfId 
   * @returns 
   */
  public updateDeviceTypeToDemo(listOfId: any): Observable<HttpResponse<any>> {
    return this.http.put<any>(this.resourceUrl + '/type/demo/' + listOfId, {}, { observe: 'response' });
  }

  /**
   * <AUTHOR>
   * @param listOfId 
   * @param lockState 
   * @returns 
   */
  public updateDeviceState(listOfId: any, lockState: boolean): Observable<HttpResponse<SuccessMessageResponse>> {
    return this.http.put<any>(this.resourceUrl + '/state/' + listOfId + '?locked=' + lockState, {}, { observe: 'response' });
  }

  /**
   * Update Device Type To test
   * 
   * <AUTHOR>
   * @param listOfId 
   * @returns 
   */
  public updateDeviceTypeToTest(listOfId: any): Observable<HttpResponse<any>> {
    return this.http.put<any>(this.resourceUrl + '/type/test/' + listOfId, {}, { observe: 'response' });
  }


  /**
   * Associate Device with Customer email
   * 
   * <AUTHOR>
   * @param listOfId 
   * @param requestBody 
   * @returns 
   */
  public associationDeviceWithSalesOrder(listOfId: number[], requestBody: BasicSalesOrderDetailResponse): Observable<HttpResponse<SuccessMessageResponse>> {
    return this.http.put<SuccessMessageResponse>(this.resourceUrl + '/soDetails/' + listOfId, requestBody, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
   * Get Device Detail By id
   * <AUTHOR>
   * @param id 
   * @returns 
   */
  public getDeviceDetail(id: number): Observable<HttpResponse<DeviceDetailResponse>> {
    return this.http.get<DeviceDetailResponse>(this.resourceUrl + '/' + id, { observe: 'response' });
  }

  /**
   * Get Release Version
   * 
   * <AUTHOR>
   * @param requestObject 
   * @returns 
   */
  public getReleaseVersionDetail(requestObject: any): Observable<HttpResponse<any>> {
    return this.http.post<any>(this.resourceUrl + '/release', requestObject, { observe: 'response' });
  }

  /**
   * Assign Release Version to Device
   * 
   * <AUTHOR>
   * @param req 
   * @returns 
   */
  public assignSelectedReleaseVersion(req: AssignSelectedReleaseVersionRequest): Observable<HttpResponse<SuccessMessageResponse>> {
    const options = createRequestOption({ releaseId: req.releaseId });
    return this.http.put<any>(this.resourceUrl + '/release/' + req.deviceId, null, { params: options, observe: 'response' })
  }

  public generateCSVFileForDevice(requestBody): Observable<HttpResponse<any>> {
    return this.http.post<any>(this.resourceUrl.concat('/generateCSV'), requestBody, { observe: 'response' });
  }

  public downloadCSVFileForDevice(fileName: string): Observable<HttpResponse<any>> {
    return this.http.get<any>(this.resourceUrl.concat('/downloadDevicesData/').concat(fileName), { responseType: 'blob' as 'json' });
  }

  /**
   * Disable Product Status For Device
   * @param deviceIds 
   * @returns 
   */
  public disableProductStatusForDevice(deviceId: Array<number>): Observable<HttpResponse<SuccessMessageResponse>> {
    return this.http.put<SuccessMessageResponse>(this.resourceUrl + '/status/disable/' + deviceId, null, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
   * Rma Product Status For Device
   * @param deviceIds 
   * @returns 
   */
  public rmaProductStatusForDevice(deviceId: Array<number>): Observable<HttpResponse<SuccessMessageResponse>> {
    return this.http.put<SuccessMessageResponse>(this.resourceUrl + '/status/rma/' + deviceId, {}, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
  * Edit Enable/Disable For Device
  * @param deviceId 
  * @returns 
  */
  public editEnableDisableForDevice(deviceId: Array<number> | number, isEnable: boolean): Observable<HttpResponse<SuccessMessageResponse>> {
    return this.http.put<SuccessMessageResponse>(this.resourceUrl + '/edit/' + deviceId + '?enable=' + isEnable, null, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }
}
