import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { CountryAndLanguageService } from 'src/app/shared/Service/CountryAndLanguageService/country-and-language.service';
import { RoleApiCallService } from 'src/app/shared/Service/RoleService/role-api-call.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { KitManagementFilterComponent } from './kit-management-filter.component';
import { EnumMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';

describe('KitManagementFilterComponent', () => {
  let component: KitManagementFilterComponent;
  let fixture: ComponentFixture<KitManagementFilterComponent>;

  beforeEach(async () => {
    const toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    const confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['open', 'close']);
    const localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);

    // Mock the return value of retrieve for the getPermissionObject function
    localStorageServiceMock.retrieve.and.returnValue('mockedPermissionObject');
    await TestBed.configureTestingModule({
      declarations: [KitManagementFilterComponent],
      imports: [NgMultiSelectDropDownModule.forRoot(), ReactiveFormsModule],
      providers: [
        CountryAndLanguageService,
        ExceptionHandlingService,
        SessionStorageService,
        CommonOperationsService,
        EnumMappingDisplayNamePipe,
        PrintListPipe,
        RoleApiCallService,
        HidePermissionNamePipe,
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(KitManagementFilterComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
