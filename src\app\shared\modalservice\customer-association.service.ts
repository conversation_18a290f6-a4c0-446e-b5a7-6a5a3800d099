import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CustomerAssociationComponent } from 'src/app/device/customer-association/customer-association.component';
import { CustomerAssociationModelRequest } from 'src/app/model/customer-association-model/customer-association-model-request.model';
import { CustomerAssociationRequest } from 'src/app/model/customer-association-request';

@Injectable({
  providedIn: 'root'
})
export class CustomerAssociationService {


  constructor(
    private modalService: NgbModal
  ) { }

  public openCustomerAssociationPopup(customerAssociationModelRequest: CustomerAssociationModelRequest,
    dialogSize: 'sm' | 'lg' = 'lg'): Promise<CustomerAssociationRequest> {
    const modalRef = this.modalService.open(CustomerAssociationComponent, { windowClass: "modal fade" });
    modalRef.componentInstance.customerAssociationModelRequest = customerAssociationModelRequest;
    return modalRef.result;
  }
}
