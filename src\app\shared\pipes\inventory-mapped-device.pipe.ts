import { Pipe, PipeTransform } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { deviceTypesEnum } from '../enum/deviceTypesEnum.enum';
import { CommonsService } from '../util/commons.service';

@Pipe({
  name: 'inventoryMappedDeviceName'
})
export class InventoryMappedDevicePipe implements PipeTransform {

  constructor(public commonService: CommonsService) { }

  transform(deviceTypes: deviceTypesEnum[]): any {
    if (isNullOrUndefined(deviceTypes) || deviceTypes.length == 0) {
      return null;
    } else {
      let deviceType = '';
      for (let index = 0; index < deviceTypes.length; index++) {
        const deviceTypeName = deviceTypes[index];
        if (index == (deviceTypes.length - 1)) {
          deviceType = deviceType + this.commonService.getDeviceTypeEnumToString(deviceTypeName);
        } else {
          deviceType = deviceType + this.commonService.getDeviceTypeEnumToString(deviceTypeName) + ', ';
        }
      }
      return deviceType;
    }
  }

}
