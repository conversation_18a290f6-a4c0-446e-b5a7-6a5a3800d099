import { Component } from '@angular/core';
import { DeviceListResource } from 'src/app/app.constants';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { DeviceSearchRequest } from 'src/app/model/device/deviceSearchRequest.model';
import { DeviceOperationService } from '../DeviceService/Device-Operation/device-operation.service';

@Component({
  selector: 'app-device-module',
  templateUrl: './device-module.component.html',
  styleUrls: ['./device-module.component.css']
})
export class DeviceModuleComponent {

  deviceSearchRequestBody: DeviceSearchRequest = null;
  isFilterHidden: boolean = false;

  isDeviceListingPageDisplay: boolean = true;
  isDeviceDetailPageDisplay: boolean = false;

  loading: boolean = false;

  deviceIdInput: number;
  deviceListResource = DeviceListResource;

  constructor(private deviceOperationService: DeviceOperationService) { }

  getDeviceId(deviceId: number): void {
    this.deviceIdInput = deviceId;
  }

  /**
    * Call Filter component subject and reload page
    * <AUTHOR>
    * @param isDefaultPageNumber
    * @param isClearFilter
    */
  public filterPageSubjectCallForReloadPage(isDefaultPageNumber: boolean, isClearFilter: boolean): void {
    let listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, isDefaultPageNumber, isClearFilter, false);
    this.deviceOperationService.callRefreshPageSubject(listingPageReloadSubjectParameter, DeviceListResource, this.isFilterHidden, this.deviceSearchRequestBody);
  }

  /**
  * Update deviceSearchRequestBody from child component
  * <AUTHOR>
  * @param value
  */
  public updateDeviceSearchRequestBody(value: DeviceSearchRequest): void {
    this.deviceSearchRequestBody = value;
  }

  /**
  * Update isFilterHidden from child component
  * <AUTHOR>
  * @param value
  */
  public updateIsFilterHidden(value: boolean): void {
    this.isFilterHidden = value;
  }

  public showDeviceDetail() {
    this.isDeviceDetailPageDisplay = true;
    this.isDeviceListingPageDisplay = false;
  }

  /**
  * Show device listing page from detail page
  * <AUTHOR>
  */
  public showDevice(): void {
    this.isDeviceListingPageDisplay = true;
    this.isDeviceDetailPageDisplay = false;
    this.filterPageSubjectCallForReloadPage(false, false)
  }

}
