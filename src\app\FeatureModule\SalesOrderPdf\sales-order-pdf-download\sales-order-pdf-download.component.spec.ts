import { DatePipe } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbActiveModal, NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { ProbeApiService } from 'src/app/shared/Service/ProbeService/probe-api.service';
import { RoleApiCallService } from 'src/app/shared/Service/RoleService/role-api-call.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { SalesOrderPdfDownloadComponent } from './sales-order-pdf-download.component';
import { EnumMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';

describe('SalesOrderPdfDownloadComponent', () => {
  let component: SalesOrderPdfDownloadComponent;
  let fixture: ComponentFixture<SalesOrderPdfDownloadComponent>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;

  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);

    await TestBed.configureTestingModule({
      declarations: [SalesOrderPdfDownloadComponent],
      imports: [NgbModule,
        NgMultiSelectDropDownModule.forRoot(),
        ReactiveFormsModule,
        FormsModule],
      providers: [NgbActiveModal,
        CommonOperationsService,
        SalesOrderPdfDownloadComponent,
        RoleApiCallService,
        RoleApiCallService,
        ExceptionHandlingService,
        AuthJwtService,
        SessionStorageService,
        HidePermissionNamePipe,
        ProbeApiService,
        DatePipe,
        LocalStorageService,
        EnumMappingDisplayNamePipe,
        PrintListPipe,
        ConfirmDialogService,
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(SalesOrderPdfDownloadComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
