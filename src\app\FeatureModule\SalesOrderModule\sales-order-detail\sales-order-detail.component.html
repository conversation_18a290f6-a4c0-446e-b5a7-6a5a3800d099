<ng-template [ngIf]="salesOrderDetailPageDiplay">
  <!------------------------------------------->
  <!-- loadin start -->
  <!------------------------------------------->
  <div class="ringLoading" *ngIf="loading">
    <!-- loading gif start -->
    <div class="ringLoadingDiv">
      <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
    </div>
    <!-- loading gif end -->
  </div>
  <!------------------------------------------->
  <!-- loadin end -->
  <!------------------------------------------->

  <body class="bg-white">
    <!------------------------------------------->
    <!-- container fluid start -->
    <!------------------------------------------->
    <div class="container-fluid" id="salesOrderDetailPage">
      <!-- row start -->
      <div class="row">
        <div class="col-md-12">
          <!-- sales order detail row start -->
          <div class="row" class="headerAlignment">
            <label class="childFlex h5-tag">Sales Order Details</label>
            <div class="childFlex">
              <!------------------------------------------------->
              <!--------------Sales Order Detail Operations------------------->
              <!------------------------------------------------->
              <div *ngIf="salesOrderDetailOperations.length > 1" class="mr-3">
                <select id="salesOrderDetailOperation" class="form-control form-control-sm"
                  (change)="changeSalesOrderOperation($event)">
                  <ng-template ngFor let-salesOrderOperation [ngForOf]="salesOrderDetailOperations">
                    <option [value]="salesOrderOperation">{{ salesOrderOperation }}</option>
                  </ng-template>
                </select>
              </div>

              <div *ngIf="downloadSalesOrderLetterPermission">
                <button class="btn btn-sm btn-outline-secondary mr-2" (click)="downloadSalesOrderPdfLetter()"
                  [disabled]="!salesOrderDetail?.soLetterDownload"
                  [title]="!salesOrderDetail?.soLetterDownload?'SO Letter can\'t be downloaded as no probe is configured with this Sales Order.':''"><i
                    class="fa fa-download" aria-hidden="true"></i>&nbsp;&nbsp;Download SO
                  Letter</button>
              </div>
              <!-- back button div start -->
              <button class="btn btn-sm btn-outline-secondary role-back-btn  mr-3" (click)="back()"
                id="salesOrderDetailBack"><i class="fa fa-reply" aria-hidden="true"></i>&nbsp;&nbsp;Back</button>
              <!-- back button div end -->
              <!-------------------------------------------->

              <button class="btn btn-sm btn-orange" (click)="refreshFilter()" id="salesOrderRefresh"><em
                  class="fa fa-refresh"></em></button>

            </div>
          </div>
          <!-- sales order detail row end -->
          <!------------------------------------------->
          <!--Row Start-->
          <!------------------------------------------->
          <div class="row">
            <!-- ms 12 column start -->
            <div class="col-md-12">
              <!-- card start -->
              <div class="card">
                <div class="card-body">
                  <!-- card shadow start -->
                  <div class="card shadow">
                    <div class="card-body">
                      <!--------------------------------------->
                      <!--------------------------------------->
                      <!--------------------------------------->
                      <!-- detail fields start -->
                      <!--------------------------------------->
                      <!--------------------------------------->
                      <!--------------------------------------->
                      <!--1 Order Number-->
                      <!--2 Order Type-->
                      <!--3 Customer Name-->
                      <!--4 Customer Email-->
                      <!--5 Country-->
                      <!--6 PO#-->
                      <!--7 Order Start Date-->
                      <!--8 Last Modified Time-->
                      <!--9 Last Synced Date & Time-->
                      <!--10 Order Status-->
                      <!--------------------------------------->
                      <!--------------------------------------->
                      <!--------------------------------------->
                      <!--------------------------------------->
                      <div class="row">
                        <div class="col-md-3">
                          <div class="form-group">
                            <label><strong class="">Order Number</strong></label>
                            <input type="text" class="form-control" name="salesOrderNumber"
                              [value]="salesOrderDetail?.salesOrderNumber" readonly>
                          </div>
                        </div>

                        <div class="col-md-3">
                          <div class="form-group">
                            <label><strong class="">Order Type</strong></label>
                            <input type="text" class="form-control" name="salesOrderNumber"
                              [value]="salesOrderDetail?.orderType == null?'':salesOrderTypeStatus[salesOrderDetail?.orderType]"
                              readonly>
                          </div>
                        </div>

                        <div class="col-md-3">
                          <div class="form-group">
                            <label><strong class="">Customer Name</strong></label>
                            <input type="text" class="form-control" name="customerName"
                              [value]="salesOrderDetail?.customerName" readonly>
                          </div>
                        </div>

                        <div class="col-md-3">
                          <div class="form-group">
                            <label><strong class="">Customer E-mail</strong></label>
                            <input type="text" class="form-control" name="customerEmail"
                              [value]="salesOrderDetail?.customerEmail" readonly>
                          </div>
                        </div>

                        <div class="col-md-3">
                          <div class="form-group">
                            <label><strong class="">Country</strong></label>
                            <input type="text" class="form-control" name="country" [value]="salesOrderDetail?.country"
                              readonly>
                          </div>
                        </div>

                        <div class="col-md-3">
                          <div class="form-group">
                            <label><strong class="">PO#</strong></label>
                            <input type="text" class="form-control" name="poNumber" [value]="salesOrderDetail?.poNumber"
                              readonly>
                          </div>
                        </div>

                        <div class="col-md-3">
                          <div class="form-group">
                            <label><strong class="">Order Start Date & Time</strong></label>
                            <input type="text" class="form-control" name="orderStartDate"
                              [value]="salesOrderDetail?.soCreatedDate | date:dateTimeDisplayFormat" readonly>
                          </div>
                        </div>

                        <div class="col-md-3">
                          <div class="form-group">
                            <label><strong class="">Last Modified Date & Time</strong></label>
                            <input type="text" class="form-control" name="lastSyncedDateTime"
                              [value]="salesOrderDetail?.modifiedDate | date:dateTimeDisplayFormat" readonly>
                          </div>
                        </div>

                        <div class="col-md-3">
                          <div class="form-group">
                            <label><strong class="">Last Synced Date & Time</strong></label>
                            <input type="text" class="form-control" name="lastSyncedDateTime"
                              [value]="salesOrderDetail?.lastSyncDate | date:dateTimeDisplayFormat" readonly>
                          </div>
                        </div>

                        <div class="col-md-3">
                          <div class="form-group">
                            <label><strong class="">Order Status</strong></label>
                            <input type="text" class="form-control" name="orderStatus"
                              [value]="salesOrderDetail?.status ==null ?'':salesOrderStatusEnum[salesOrderDetail?.status]"
                              readonly>
                          </div>
                        </div>

                        <div class="col-md-3">
                          <div class="form-group">
                            <label><strong class="">SO Config Status</strong></label>
                            <input type="text" class="form-control" name="productStatus"
                              [value]="salesOrderDetail?.soStatus?productConfigStatus[salesOrderDetail?.soStatus]:''"
                              readonly>
                          </div>
                        </div>

                        <div class="col-md-3">
                          <!-- Form Group for Device Auto Lock -->
                          <div class="form-group">
                            <!-- Label for Device Auto Lock -->
                            <label><strong class="">Device Auto Lock</strong></label>
                            <!-- Input field for Device Auto Lock, bound to salesOrderDetail?.deviceAutoLock with a pipe to display lock/unlock status -->
                            <input type="text" class="form-control" name="DeviceAutoLock"
                              [value]="salesOrderDetail?.deviceAutoLock| booleanKeyValueMappingDisplayNamePipe:lockUnlockStatus"
                              readonly>
                          </div>
                        </div>

                        <div class="col-md-3">
                          <!-- Form Group for Probe Auto Locks -->
                          <div class="form-group">
                            <!-- Label for Probe Auto Lock -->
                            <label><strong class="">Probe Auto Lock</strong></label>
                            <!-- Input field for Probe Auto Lock, bound to salesOrderDetail?.probeAutoLock with a pipe to display lock/unlock status -->
                            <input type="text" class="form-control" name="probeAutoLock"
                              [value]="salesOrderDetail?.probeAutoLock| booleanKeyValueMappingDisplayNamePipe:lockUnlockStatus"
                              readonly>
                          </div>
                        </div>

                        <div class="col-md-3">
                          <div class="form-group">
                            <label><strong class="">Order Record Type</strong></label>
                            <input type="text" class="form-control" name="productStatus"
                              [value]="salesOrderDetail?.orderRecordType" readonly>
                          </div>
                        </div>

                      </div>
                      <!-------------------------------------->
                      <!-------------------------------------->
                      <!-- detail fields end -->
                      <!-------------------------------------->
                      <!-------------------------------------->

                      <!--form end-->
                    </div>
                  </div>
                  <!-- card shadow end -->
                </div>
              </div>
              <!-- card end -->
            </div>
            <!-- ms 12 column end -->
          </div>
          <!------------------------------------------->
          <!--Row End-->
          <!------------------------------------------->


          <!-- Related Products row start -->
          <ng-template [ngIf]="relatedProductList != null && relatedProductList.length > 0">
            <div class="row">
              <div class="col-md-12">
                <!-- main card start -->
                <div class="card">
                  <div class="card-body">
                    <div class="card shadow">
                      <div class="card-body">
                        <div class="container-fuild ml-4 mr-4">
                          <label class="mb-1 h5-tag"><span>Related Probe(s)</span>
                          </label>
                          <hr class="hrMargin">
                          <!-- ------------------------------------------------------- -->
                          <!-- table start -->
                          <!-- ------------------------------------------------------- -->
                          <!-------------------------------------->
                          <!---------------Total Probe(s)-------->
                          <div class="bottomMargin-5">Total {{relatedProductList.length}} Related Probe(s)</div>
                          <form [formGroup]="formGroup" class="salesDeatilsTable">
                            <ng-container formArrayName="serialNumberList">
                              <table class="table table-sm table-bordered mr-1" id="relatedProduct" aria-hidden="true">
                                <!-- table header start -->
                                <thead>
                                  <tr class="thead-light">
                                    <th>Sr. No.</th>
                                    <th class="nowrap">Probe Type</th>
                                    <th class="nowrap">Part Number</th>
                                    <th class="nowrap">Bridge Kit Part Number</th>
                                    <th class="nowrap">OTS Kit Part Number</th>
                                    <th class="nowrap">Preset(s)</th>
                                    <th class="nowrap">Feature(s)</th>
                                    <th class="nowrap">Config Groups(s)</th>
                                    <th class="nowrap min_action">Serial Number</th>
                                    <th class="nowrap" *ngIf="transferSalesOrder">
                                      Transferred Order Number</th>
                                    <th class="nowrap">Status</th>
                                    <th>Action</th>
                                  </tr>
                                </thead>
                                <!-- table header end -->
                                <!-- table body start -->
                                <tbody>
                                  <tr *ngFor='let relatedProduct of relatedProductList;let i=index'>
                                    <td>{{i+1}}</td>
                                    <td class="nowrap">{{relatedProduct.probeType}}</td>
                                    <td class="nowrap">{{relatedProduct.productCode}}</td>
                                    <td class="nowrap">{{relatedProduct.bridgeKitPartNumberCode}}</td>
                                    <td class="nowrap">{{relatedProduct.otsKitPartNumberCode}}</td>
                                    <td class="nowrap"
                                      [innerHTML]="relatedProduct.associatedPresets | featureInformationDisplayPipe">
                                    </td>
                                    <td class="nowrap"
                                      [innerHTML]="relatedProduct.associatedFeatures | featureInformationDisplayPipe">
                                    </td>
                                    <td>{{relatedProduct.probeConfigGroupPartNumberCodes}}</td>
                                    <td style="height: 48.8px; width: 30%;" class="nowrap">
                                      <ng-template [ngIf]="salesOrderProbeCreatePermission">
                                        <div [formGroupName]="i">

                                          <div class="headerAlignment mb-0" *ngIf="!relatedProduct.isEditMode">
                                            <div class="childFlex">
                                              <div>
                                                <span style="vertical-align: sub;"
                                                  *ngIf="relatedProduct.entitySerialNumber == null">[Yet to be
                                                  Assign]</span>
                                                <span style="vertical-align: sub;"
                                                  *ngIf="relatedProduct.entitySerialNumber != null">{{relatedProduct.entitySerialNumber}}</span>
                                              </div>
                                              <ng-template
                                                [ngIf]="relatedProduct.entityStatus == null || productConfigStatusEnum[relatedProduct.entityStatus] == productNotConfig">
                                                <button class="btn btn-sm ml-2" id="addSalesOrderBtn"
                                                  (click)="editSerialNumber(relatedProduct.sopmId,i)">
                                                  <em class="fa fa-pencil-alt"
                                                    style="color: #f79423; font-size: 15px;"></em>
                                                </button>
                                              </ng-template>
                                            </div>
                                          </div>
                                          <div class="headerAlignment mb-0" *ngIf="relatedProduct.isEditMode">
                                            <div class="childFlex w-100">
                                              <div class="form-group mb-0 w-100">
                                                <div>
                                                  <input type="text" class="form-control" name="probeSerialNumber"
                                                    formControlName="serialNumber" autocomplete="off"
                                                    (input)="setSerailNumberLocalData(i,relatedProduct.sopmId,$any($event.target)?.value)">
                                                </div>
                                                <div *ngIf="relatedProduct?.apiErrorMessage != null">
                                                  <span class="alert-color">{{relatedProduct?.apiErrorMessage}}</span>
                                                </div>
                                                <div
                                                  *ngIf="(formGroup.get('serialNumberList')['controls'][i].get('serialNumber').touched
                                                || formGroup.get('serialNumberList')['controls'][i].get('serialNumber').dirty) &&
                                                formGroup.get('serialNumberList')['controls'][i].get('serialNumber').invalid">
                                                  <span class="alert-color"
                                                    *ngIf="formGroup.get('serialNumberList')['controls'][i].get('serialNumber').errors['required']">
                                                    {{enterQRCode}}</span>
                                                  <!---------------------------->
                                                  <!-----------maxlength-------->
                                                  <!---------------------------->
                                                  <span
                                                    *ngIf="formGroup.get('serialNumberList')['controls'][i].get('serialNumber').errors['maxlength']"
                                                    class="alert-color">
                                                    {{small_maxCharactersAllowedMessage}}</span>
                                                  <!---------------------------->
                                                  <!-----------pattern---------->
                                                  <!---------------------------->
                                                  <span class="alert-color"
                                                    *ngIf="(!formGroup.get('serialNumberList')['controls'][i].get('serialNumber').errors['serialNumberexists'] &&
                                                    formGroup.get('serialNumberList')['controls'][i].get('serialNumber').errors['pattern'])">
                                                    {{enterValidSerialNumber}}
                                                  </span>
                                                  <!---------------------------->
                                                  <!-----------cannotContainSpace-------->
                                                  <!---------------------------->
                                                  <span class="alert-color"
                                                    *ngIf="(!formGroup.get('serialNumberList')['controls'][i].get('serialNumber').errors['serialNumberexists'] &&
                                                      formGroup.get('serialNumberList')['controls'][i].get('serialNumber').errors['cannotContainSpace'])">
                                                    {{enterValidSerialNumber}}
                                                  </span>
                                                  <span class="alert-color" id="SalesOrderDetailserialNumberExists"
                                                    *ngIf="(formGroup.get('serialNumberList')['controls'][i].get('serialNumber').errors['serialNumberexists'])">
                                                    {{serialNumberExists}}
                                                  </span>
                                                  <span class="alert-color"
                                                    *ngIf="((!formGroup.get('serialNumberList')['controls'][i].get('serialNumber').errors['pattern'] &&
                                                    !formGroup.get('serialNumberList')['controls'][i].get('serialNumber').errors['serialNumberexists']) &&
                                                    formGroup.get('serialNumberList')['controls'][i].get('serialNumber').errors['serialNumberStartWith'])">
                                                    {{formGroup.get('serialNumberList')['controls'][i].get('serialNumber').errors['message']}}
                                                  </span>
                                                </div>
                                              </div>
                                              <button class=" btn btn-sm ml-2"
                                                (click)="saveSerialNumber(i,relatedProduct.sopmId)"
                                                id="saveOneSalesOrderBtn">
                                                <em class="fa fa-save" style="color: #f79423;font-size: 17px;"></em>
                                              </button>
                                              <button class=" btn btn-sm ml-2"
                                                (click)="closeSingleEditMode(i,relatedProduct.sopmId)"
                                                id="salesOrderDetailCloseSarialNumber">
                                                <em class="fa fa-close" style="color: #f79423;font-size: 17px;"></em>
                                              </button>
                                            </div>
                                          </div>

                                        </div>
                                      </ng-template>
                                      <!--------------------------------------------------->
                                      <!--Read Only Permission Only Serial Number Display-->
                                      <!--------------------------------------------------->
                                      <ng-template [ngIf]="!salesOrderProbeCreatePermission">
                                        <div>
                                          <span style="vertical-align: sub;"
                                            *ngIf="relatedProduct.entitySerialNumber == null">[Yet to be
                                            Assign]</span>
                                          <span style="vertical-align: sub;"
                                            *ngIf="relatedProduct.entitySerialNumber != null">{{relatedProduct.entitySerialNumber}}</span>
                                        </div>
                                      </ng-template>
                                      <!--------------------------------------------------->
                                      <!--Read Only Permission Only Serial Number Display-->
                                      <!--------------------------------------------------->
                                    </td>
                                    <td class="nowrap" *ngIf="transferSalesOrder">
                                      {{relatedProduct.destinationSalesOrderNumber}}
                                    </td>
                                    <td class="nowrap">{{relatedProduct?.entityStatus |salesOrderStatusDisplay}}
                                    </td>
                                    <td class="nowrap">
                                      <ng-template [ngIf]="relatedProduct?.entityPk != null && probePermission">
                                        <div class="spanunderline" id="salesOrderDetailToProbeDetail"
                                          (click)="showProbeDetailPage(relatedProduct.entityPk)">
                                          View Details</div>
                                      </ng-template>
                                    </td>
                                  </tr>
                                </tbody>
                                <!-- table body end -->
                              </table>
                            </ng-container>
                          </form>

                          <!-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
                          <ng-template [ngIf]="salesOrderProbeCreatePermission && localSerialNumberMapping.length > 0">
                            <div class="row mt-3">
                              <div class="col-md-8 col-sm-8">
                              </div>
                              <div class="col-md-4 col-sm-4">
                                <div class="multiprobe-div">
                                  <!---------------------------------->
                                  <!----------1 edit button----------->
                                  <!----------2 Close button---------->
                                  <!---------------------------------->
                                  <button class="btn btn-sm btn-outline-secondary mr-2" style="text-align: center;"
                                    id="closeAllSalesOrderEdit" (click)="closeEditMode()"><span
                                      class="Pointer">Close</span></button>
                                  <button class="btn btn-sm btn-orange" style="text-align: center;"
                                    id="salesOrderDetailSaveAllSarialNumber" [disabled]="!formIsValid"
                                    (click)="saveMultipleSerialNumber()"><span class="Pointer">Save</span></button>
                                  <!---------------------------------->
                                  <!---------------------------------->
                                  <!---------------------------------->
                                </div>
                              </div>
                            </div>
                          </ng-template>
                          <!-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->

                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- main card end -->
              </div>
            </div>
          </ng-template>
          <!-- Related Products row end -->

          <!---##############################################################-->
          <!----------------------Related Bridges Start------------------------>
          <!--                                                               -->
          <!---##############################################################-->
          <ng-template [ngIf]="bridgesResponse != null && bridgesResponse.length > 0">
            <div class="row">
              <div class="col-md-12">
                <!-- main card start -->
                <div class="card">
                  <div class="card-body">
                    <div class="card shadow">
                      <div class="card-body">
                        <div class="container-fuild ml-4 mr-4">
                          <label class="mb-1 h5-tag"><span>Related Bridge(s)</span>
                          </label>
                          <hr class="hrMargin">
                          <!-------------------------------------->
                          <!-------------------------------------->
                          <!-------------------------------------->
                          <!---------------Total Bridge(s)-------->
                          <div class="bottomMargin-5">Total {{bridgesResponse.length}} Related Bridge(s)</div>
                          <!-------------------------------------->
                          <table class="table table-sm table-bordered" aria-hidden="true" id="bridgeTable">
                            <!-- table header start -->
                            <thead>
                              <tr class="thead-light">
                                <th style="width: 5%;">Sr. No.</th>
                                <th style="width: 10%;">Part Number</th>
                                <th style="width: 20%;">Bridge Kit Part Number</th>
                                <th style="width: 15%;">Serial Number</th>
                                <th style="width: 15%;" *ngIf="transferSalesOrder">
                                  Transferred Order Number</th>
                                <th style="width: 20%;">Status</th>
                                <th style="width: 15%;">Action</th>
                              </tr>
                            </thead>
                            <!-- table header end -->
                            <!-- table body start -->
                            <tbody>
                              <tr *ngFor='let relatedBridges of bridgesResponse;let srNumber=index'>
                                <td style="width: 5%;">{{srNumber+1}}</td>
                                <td style="width: 10%;">{{relatedBridges?.productCode}}</td>
                                <td style="width: 20%;">{{relatedBridges?.bridgeKitPartNumberCode}}</td>
                                <td style="width: 15%;">{{relatedBridges?.entitySerialNumber}}</td>
                                <td style="width: 15%;" *ngIf="transferSalesOrder">
                                  {{relatedBridges?.destinationSalesOrderNumber}}</td>
                                <td style="width: 20%;">{{relatedBridges?.entityStatus | salesOrderStatusDisplay}}</td>
                                <td style="width: 10%;">
                                  <ng-template
                                    [ngIf]="(relatedBridges?.entityPk | salesOrderBridgeViewDetailButtonDisplayPipe : relatedBridges?.entityStatus) && devicePermission">
                                    <div class="spanunderline" (click)="showDeviceDetailPage(relatedBridges.entityPk)"
                                      id="salesorderDetailToDeviceDetail">
                                      View
                                      Details</div>
                                  </ng-template>
                                  <ng-template
                                    [ngIf]="  relatedBridges?.entityStatus| salesOrderBridgeResetButtonDisplayPipe  : resetButtonPermission">
                                    <button class="btn btn-sm btn-orange w-100" id="resetBridge"
                                      (click)="resetBridge(srNumber,relatedBridges?.sopmId)">
                                      Reset</button>
                                  </ng-template>
                                </td>
                              </tr>
                            </tbody>
                            <!-- table body start -->
                          </table>
                          <!-------------------------------------->
                          <!-------------------------------------->
                          <!-------------------------------------->
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ng-template>
          <!---##############################################################-->
          <!---##############################################################-->
          <!-----------------------Related Bridges End------------------------->
          <!---##############################################################-->
          <!---##############################################################-->


          <!---##############################################################-->
          <!----------------------Related Licenses Start------------------------>
          <!--                                                                -->
          <!---##############################################################--->
          <ng-template [ngIf]="licensesResponse != null && licensesResponse.length > 0">
            <div class="row">
              <div class="col-md-12">
                <!-- main card start -->
                <div class="card">
                  <div class="card-body">
                    <div class="card shadow">
                      <div class="card-body">
                        <div class="container-fuild ml-4 mr-4">
                          <label class="mb-1 h5-tag"><span>Related Licence(s)</span>
                          </label>
                          <hr class="hrMargin">
                          <!-------------------------------------->
                          <!-------------------------------------->
                          <!-------------------------------------->
                          <!---------------Total Licence(s)-------->
                          <div class="bottomMargin-5">Total {{licensesResponse.length}} Related License(s)</div>
                          <!-------------------------------------->
                          <table class="table table-sm table-bordered" aria-hidden="true" id="licenseTable">
                            <!-- table header start -->
                            <thead>
                              <tr class="thead-light">
                                <th class="nowrap">Sr. No.</th>
                                <th class="nowrap">Serial Number</th>
                                <th class="nowrap">Preset(s)</th>
                                <th class="nowrap">Feature(s)</th>
                                <th class="nowrap">Status</th>
                                <th class="nowrap">Action</th>

                              </tr>
                            </thead>
                            <!-- table header end -->
                            <!-- table body start -->
                            <tbody>
                              <tr *ngFor='let relatedLicence of licensesResponse;let srNumber=index'>
                                <td class="nowrap">{{srNumber+1}}</td>
                                <td class="nowrap">{{relatedLicence?.entitySerialNumber}}</td>
                                <td class="nowrap"
                                  [innerHTML]="relatedLicence.associatedPresets | featureInformationDisplayPipe">
                                </td>
                                <td class="nowrap"
                                  [innerHTML]="relatedLicence.associatedFeatures | featureInformationDisplayPipe">
                                </td>
                                <td class="nowrap">{{relatedLicence?.entityStatus | salesOrderStatusDisplay}}</td>
                                <td class="nowrap">
                                  <ng-template [ngIf]="relatedLicence?.entityPk != null && probePermission">
                                    <div class="spanunderline" id="salesOrderDetailToProbeDetail"
                                      (click)="showProbeDetailPage(relatedLicence.entityPk)">
                                      View Details</div>
                                  </ng-template>
                                </td>
                              </tr>
                            </tbody>
                            <!-- table body start -->
                          </table>
                          <!-------------------------------------->
                          <!-------------------------------------->
                          <!-------------------------------------->
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ng-template>
          <!---##############################################################-->
          <!---##############################################################-->
          <!-----------------------Related Licenses End------------------------>
          <!---##############################################################-->
          <!---##############################################################-->

          <!---##############################################################-->
          <!------------Related Parent Sales Order Start------------------------>
          <!--                                                                -->
          <!---##############################################################--->
          <ng-template [ngIf]="parentSalesOrders != null && parentSalesOrders.length > 0">
            <div class="row">
              <div class="col-md-12">
                <!-- main card start -->
                <div class="card">
                  <div class="card-body">
                    <div class="card shadow">
                      <div class="card-body">
                        <div class="container-fuild ml-4 mr-4">
                          <label class="mb-1 h5-tag"><span>Related Parent Sales Order(s)</span>
                          </label>
                          <hr class="hrMargin">
                          <!-------------------------------------->
                          <!-------------------------------------->
                          <!-------------------------------------->
                          <!---------------Total Parent(s)-------->
                          <div class="bottomMargin-5">Total {{parentSalesOrders.length}} Related Parent Sales Order(s)
                          </div>
                          <!-------------------------------------->
                          <table class="table table-sm table-bordered mb-0" aria-hidden="true" id="licenseTable">
                            <!-- table header start -->
                            <thead>
                              <tr class="thead-light">
                                <th class="nowrap">Sr. No.</th>
                                <th class="nowrap">Order Number</th>
                                <th class="nowrap">Order Type</th>
                                <th class="nowrap">PO#</th>
                                <th class="nowrap">SO Config Status</th>
                                <th class="nowrap">Order Start Date & Time</th>
                                <th class="nowrap">Last Modified Date & Time</th>
                                <th class="nowrap">Last Synced Date & Time</th>
                                <th class="nowrap">Action</th>

                              </tr>
                            </thead>
                            <!-- table header end -->
                            <!-- table body start -->
                            <tbody>
                              <tr *ngFor='let relatedParent of parentSalesOrders;let srNumber=index'>
                                <td class="nowrap">{{srNumber+1}}</td>
                                <td class="nowrap">{{relatedParent?.salesOrderNumber}}</td>
                                <td class="nowrap">{{relatedParent?.orderType ?
                                  salesOrderTypeStatus[relatedParent?.orderType]:''}} </td>
                                <td class="nowrap">{{relatedParent?.poNumber}}</td>
                                <td class="nowrap">
                                  {{relatedParent?.soStatus?productConfigStatus[relatedParent?.soStatus]:''}}</td>
                                <td class="nowrap"> {{relatedParent?.soCreatedDate | date:dateTimeDisplayFormat}}</td>
                                <td class="nowrap">{{relatedParent?.modifiedDate | date:dateTimeDisplayFormat}}</td>
                                <td class="nowrap">{{relatedParent?.lastSyncDate | date:dateTimeDisplayFormat}} </td>
                                <td class="nowrap">
                                  <ng-template [ngIf]="relatedParent?.id != null && relatedParent?.salesforceOrder">
                                    <div class="spanunderline" id="salesOrderDetailToProbeDetail"
                                      (click)="salesOrderChnage(relatedParent.id)">
                                      View Details</div>
                                  </ng-template>
                                </td>
                              </tr>
                            </tbody>
                            <!-- table body start -->
                          </table>
                          <span class="text-primary">*No details will be visible for this Parent Sales Order as it
                            is a manual order.</span>
                          <!-------------------------------------->
                          <!-------------------------------------->
                          <!-------------------------------------->
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ng-template>
          <!---##############################################################-->
          <!---##############################################################-->
          <!------------Related Parent Sales Order End------------------------->
          <!---##############################################################-->
          <!---##############################################################-->


          <!---##############################################################-->
          <!------------Related Child Sales Order Start------------------------>
          <!--                                                               -->
          <!---#############################################################--->
          <ng-template [ngIf]="childSalesOrders != null && childSalesOrders.length > 0">
            <div class="row">
              <div class="col-md-12">
                <!-- main card start -->
                <div class="card">
                  <div class="card-body">
                    <div class="card shadow">
                      <div class="card-body">
                        <div class="container-fuild ml-4 mr-4">
                          <label class="mb-1 h5-tag"><span>Related Child Sales Order(s)</span>
                          </label>
                          <hr class="hrMargin">
                          <!-------------------------------------->
                          <!-------------------------------------->
                          <!-------------------------------------->
                          <!---------------Total Child(s)--------->
                          <div class="bottomMargin-5">Total {{childSalesOrders.length}} Related Child Sales Order(s)
                          </div>
                          <!-------------------------------------->
                          <table class="table table-sm table-bordered mb-0" aria-hidden="true" id="licenseTable">
                            <!-- table header start -->
                            <thead>
                              <tr class="thead-light">
                                <th class="nowrap">Sr. No.</th>
                                <th class="nowrap">Order Number</th>
                                <th class="nowrap">Order Type</th>
                                <th class="nowrap">PO#</th>
                                <th class="nowrap">SO Config Status</th>
                                <th class="nowrap">Order Start Date & Time</th>
                                <th class="nowrap">Last Modified Date & Time</th>
                                <th class="nowrap">Last Synced Date & Time</th>
                                <th class="nowrap">Action</th>

                              </tr>
                            </thead>
                            <!-- table header end -->
                            <!-- table body start -->
                            <tbody>
                              <tr *ngFor='let relatedChild of childSalesOrders;let srNumber=index'>
                                <td class="nowrap">{{srNumber+1}}</td>
                                <td class="nowrap">{{relatedChild?.salesOrderNumber}}</td>
                                <td class="nowrap">{{relatedChild?.orderType ?
                                  salesOrderTypeStatus[relatedChild?.orderType]:''}} </td>
                                <td class="nowrap">{{relatedChild?.poNumber}}</td>
                                <td class="nowrap">
                                  {{relatedChild?.soStatus?productConfigStatus[relatedChild?.soStatus]:''}}</td>
                                <td class="nowrap"> {{relatedChild?.soCreatedDate | date:dateTimeDisplayFormat}}</td>
                                <td class="nowrap">{{relatedChild?.modifiedDate | date:dateTimeDisplayFormat}}</td>
                                <td class="nowrap">{{relatedChild?.lastSyncDate | date:dateTimeDisplayFormat}} </td>
                                <td class="nowrap">
                                  <ng-template [ngIf]="relatedChild?.id != null && relatedChild?.salesforceOrder">
                                    <div class="spanunderline" id="salesOrderDetailToProbeDetail"
                                      (click)="salesOrderChnage(relatedChild.id)">
                                      View Details</div>
                                  </ng-template>
                                </td>
                              </tr>
                            </tbody>
                            <!-- table body start -->
                          </table>
                          <span class="text-primary">*No details will be visible for this Parent Sales Order as it is
                            a manual order.</span>
                          <!-------------------------------------->
                          <!-------------------------------------->
                          <!-------------------------------------->
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ng-template>
          <!---##############################################################-->
          <!---##############################################################-->
          <!------------Related Chlid Sales Order End-------------------------->
          <!---##############################################################-->
          <!---##############################################################-->
        </div>
      </div>
      <!------------------------------------------->
      <!-- row end -->
      <!------------------------------------------->
    </div>
    <!------------------------------------------->
    <!-- container fluid end -->
    <!------------------------------------------->
  </body>
</ng-template>

<!----------------------------------------------------------->
<!-----------Probe Detail Page Start------------------------->
<!----------------------------------------------------------->
<ng-template [ngIf]="probeDetailPageDiplay">
  <app-ots-probes-detail [probeId]="productEntityId" [resource]="detailSalesOrderResource"
    (showOtsProbe)="showSalesOrderDetailPage()"></app-ots-probes-detail>
</ng-template>
<!----------------------------------------------------------->
<!-----------Probe Detail Page End------------------------->
<!----------------------------------------------------------->

<!------------------------------------------------------------->
<!------------Device Detail Page Start------------------------->
<!------------------------------------------------------------->
<ng-template [ngIf]="deviceDetailPageDiplay">
  <app-device-detail [deviceIdInput]="productEntityValue" [resource]="detailSalesOrderResource"
    (showDevice)="showSalesOrderDetailPage()">
  </app-device-detail>
</ng-template>
<!------------------------------------------------------------>
<!------------Device Detail Page End--------------------------->
<!------------------------------------------------------------->

<!------------------------------------------------------------->
<!----------- Tranfer Order Module Start ---------------------->
<!------------------------------------------------------------->
<ng-template [ngIf]="transferOrderSelectionDisaplay">
  <app-transfer-order-module (showTranferOrder)="transferOrderSelectionToggle(true,false)"
    [transferProductDetail]="transferProductDetail">
  </app-transfer-order-module>
</ng-template>
<!------------------------------------------------------------>
<!--------- Tranfer Order Module End-------------------------->
<!------------------------------------------------------------>