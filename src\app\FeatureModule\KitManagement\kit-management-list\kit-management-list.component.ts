import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ChangeDetectorRef, Component, EventEmitter, OnInit, Output } from '@angular/core';
import { isUndefined } from 'is-what';
import { Subscription } from 'rxjs';
import { ITEMS_PER_PAGE, ListKitManagementResource } from 'src/app/app.constants';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { KitManagemantFilterAction } from 'src/app/model/KitManagement/KitManagemantFilterAction.model';
import { KitManagemantSearchRequestBody } from 'src/app/model/KitManagement/KitManagemantSearchRequestBody.model';
import { KitManagementPageResponse } from 'src/app/model/KitManagement/KitManagementPageResponse.model';
import { KitManagementResponse } from 'src/app/model/KitManagement/KitManagementResponse.model';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { collapseFilterTextEnum } from 'src/app/shared/enum/collapseFilterButtonText.enum';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { CountryCacheService } from 'src/app/shared/Service/CacheService/countrycache.service';
import { CountryAndLanguageService } from 'src/app/shared/Service/CountryAndLanguageService/country-and-language.service';
import { KitManagemantApiCallService } from 'src/app/shared/Service/KitManagemant/kit-managemant-api-call.service';
import { KitManagemantService } from 'src/app/shared/Service/KitManagemant/kit-managemant.service';
import { CommonCheckboxService } from 'src/app/shared/util/common-checkbox.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';

@Component({
  selector: 'app-kit-management-list',
  templateUrl: './kit-management-list.component.html',
  styleUrls: ['./kit-management-list.component.css']
})
export class KitManagementListComponent implements OnInit {

  @Output('showOtsWorldList') showOtsWorldList = new EventEmitter();

  loading: boolean = false;

  //Page
  itemsPerPage: number = 0;
  page: number = 0;
  previousPage: number = 1;
  totalItems: number = 0;

  //Totel Count Display
  totalRecordDisplay: number = 0;
  totalRecord: number = 0;

  //Page Size DropDown
  dataSizes: string[] = [];
  drpselectsize: number = ITEMS_PER_PAGE;

  //Operation List
  operationsList: string[] = [];

  //Filter
  isFilterComponentInitWithApicall: boolean = true;
  listPageRefreshForbackToDetailPage: boolean = false;
  isFilterHidden: boolean = true;
  hideShowFilterButtonText: string = collapseFilterTextEnum.HIDE_FILTER;

  //selected Role Id Collect
  selectedKitIdList: number[] = [];
  localKitIdListArray: number[] = [];

  //unique CheckBox Name
  chkPreFix = "kit";
  selectAllCheckboxId = "selectAllkit";
  checkboxListName = "kitItem[]";

  //Hide Show List and Detail Page
  bridgeWorldDetailDisplay: boolean = false;
  bridgeWorldListDisplay: boolean = false;
  kitId: number = null;

  kitResponseList: Array<KitManagementResponse> = [];

  //permissions
  otsKitDisplayPermissions: boolean = false;

  //subscription
  subscriptionForLoading: Subscription;
  subscriptionForKitListFilterRequestParameter: Subscription;

  //kit Rev Version 
  kitRevVersionResponse: string = null;

  //kit serach request body store
  kitManagemantSearchRequestBody: KitManagemantSearchRequestBody = null;

  //checkboxDisplay
  isCheckBoxDiaply: boolean = false;

  constructor(
    private authservice: AuthJwtService,
    private commonsService: CommonsService,
    private commonOperationsService: CommonOperationsService,
    private kitManagemantService: KitManagemantService,
    private kitManagemantApiCallService: KitManagemantApiCallService,
    private exceptionService: ExceptionHandlingService,
    private commonCheckboxService: CommonCheckboxService,
    private permissionService: PermissionService,
    private cdr: ChangeDetectorRef,
    private countryCacheService: CountryCacheService,
    private countryAndLanguageService: CountryAndLanguageService) {
  }

  /**
  * Init methoad
  * 
  * <AUTHOR>
  */
  public ngOnInit(): void {
    if (!this.authservice.isAuthenticate()) {
      this.authservice.loginNavigate();
    } else {
      this.page = 0;
      this.dataSizes = this.commonsService.accessDataSizes();
      this.operationsList = this.commonOperationsService.accessKitOperations(true);
      this.isFilterComponentInitWithApicall = true;
      this.otsKitDisplayPermissions = this.permissionService.getKitManagementPermission(PermissionAction.GET_OTS_KIT_MANAGEMENT_ACTION);
      this.listPageRefreshForbackToDetailPage = false;
      this.isFilterHidden = false;
      this.bridgeWorldListDisplay = true;
      this.bridgeWorldDetailDisplay = false;
      this.itemsPerPage = ITEMS_PER_PAGE;
      this.drpselectsize = ITEMS_PER_PAGE;
      this.selectedKitIdList = [];
      this.refreshFilter();
      this.getKitRevVersion();
    }
    this.subjectInit();
  }

  private subjectInit(): void {
    /**
     * Loading Hide/Display
     * <AUTHOR>
     */
    this.subscriptionForLoading = this.commonOperationsService.getCommonLoadingSubject()?.subscribe((res: boolean) => {
      this.setLoadingStatus(res);
    });

    /**
     * This Subject call from Filter component
     * Load all the Data
     * 
     * <AUTHOR>
     */
    this.subscriptionForKitListFilterRequestParameter = this.kitManagemantService.getKitListFilterRequestParameterSubject()?.subscribe((kitRequestParameter: KitManagemantFilterAction) => {
      if (kitRequestParameter.listingPageReloadSubjectParameter.isReloadData) {
        if (kitRequestParameter.listingPageReloadSubjectParameter.isDefaultPageNumber) {
          this.selectedKitIdList = [];
          this.resetPage()
        }
        if (kitRequestParameter.listingPageReloadSubjectParameter.isOtherAction) {
          this.getKitRevVersion();
        }
        this.loadAll(kitRequestParameter.roleRequestBody);
      }
    });
  }

  /**
  * Refresh button click
  *
  * <AUTHOR>
  */
  public async clickOnRefreshButton(): Promise<void> {
    this.resetPage();
    this.getKitRevVersion();
    // Filter is hidden, directly update the service cache
    const countriesList = await this.countryCacheService.getCountryListFromCache();
    const languagesList = await this.countryAndLanguageService.getLanguageList();
    this.kitManagemantService.setCountryList(countriesList);
    this.kitManagemantService.setLanguageList(languagesList);

    this.filterPageSubjectCallForReloadPage(true, false);
  }

  /**
   * Destroy subscription
   *
   * <AUTHOR>
   */
  public ngOnDestroy(): void {
    if (!isUndefined(this.subscriptionForLoading)) { this.subscriptionForLoading.unsubscribe() }
    if (!isUndefined(this.subscriptionForKitListFilterRequestParameter)) { this.subscriptionForKitListFilterRequestParameter.unsubscribe() }
    this.kitManagemantService.setCountryList([]);
    this.kitManagemantService.setLanguageList([]);
  }

  /**
  * Get Rev Version 
  *  
  * @returns 
  */
  public async getKitRevVersion(): Promise<void> {
    this.kitRevVersionResponse = await this.kitManagemantApiCallService.getKitRevVersion();
  }

  /**
   * Kit Operation like import csv
   * 
   * <AUTHOR>
   * 
   * @param operationName 
   */
  public changeOperation(operationName: string): void {
    let listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, true, false, true)
    this.commonOperationsService.commonOperationForKitManagement(operationName, listingPageReloadSubjectParameter, ListKitManagementResource, this.isFilterHidden);
  }

  /**
   * Clear all filter ,Reset Page and Reload the page
   * 
   * <AUTHOR>
   */
  public refreshFilter(): void {
    this.resetPage();
    this.filterPageSubjectCallForReloadPage(true, true);
  }

  /**
   * Item par page Value Changes like (10,50,100)
   * 
   * <AUTHOR>
   * @param datasize 
   */
  public changeDataSize(datasize): void {
    this.setLoadingStatus(true);
    this.selectedKitIdList = [];
    this.itemsPerPage = datasize.target.value;
    this.filterPageSubjectCallForReloadPage(true, false);
  }



  /**
  * Reset Page
  * 
  * <AUTHOR>
  */
  private resetPage(): void {
    this.page = 0;
    this.previousPage = 1;
  }

  /**
    * Change The Page
    * callKitListRefreshSubject ->Call the filter component
    * filter not clear and send with filter requrest and load data 
    * 
    * <AUTHOR>
    * 
    * @param page 
    */
  public loadPage(page): void {
    if (page !== this.previousPage) {
      this.previousPage = page;
      this.commonCheckboxService.clearSelectAllCheckbox(this.selectAllCheckboxId);
      this.filterPageSubjectCallForReloadPage(false, false);
    }
  }

  /**
   * Call Filter component subject and reload page
   * 
   * <AUTHOR>
   * 
   * @param isDefaultPageNumber 
   * @param isClearFilter 
   */
  public filterPageSubjectCallForReloadPage(isDefaultPageNumber: boolean, isClearFilter: boolean): void {
    let listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, isDefaultPageNumber, isClearFilter, false)
    this.kitManagemantService.callRefreshPageSubject(listingPageReloadSubjectParameter, ListKitManagementResource, this.isFilterHidden);
  }

  /**
  * Toggle Filter
  * 
  * <AUTHOR>
  * 
  * @param id 
  */
  public toggleFilter(): void {
    this.isFilterComponentInitWithApicall = false;
    this.listPageRefreshForbackToDetailPage = false;
    this.isFilterHidden = !this.isFilterHidden;
    if (this.isFilterHidden) {
      this.hideShowFilterButtonText = collapseFilterTextEnum.SHOW_FILTER;
    } else {
      this.hideShowFilterButtonText = collapseFilterTextEnum.HIDE_FILTER;
    }
  }

  /**
   * Get Kit List
   * 
   * <AUTHOR>
   * 
   * @param kitManagemantSearchRequestBody 
   */
  public loadAll(kitManagemantSearchRequestBody: KitManagemantSearchRequestBody): void {
    this.kitManagemantSearchRequestBody = kitManagemantSearchRequestBody;
    let pageObj = {
      page: this.page - 1,
      size: this.itemsPerPage
    }
    this.setLoadingStatus(true);
    this.kitManagemantApiCallService.getKitList(kitManagemantSearchRequestBody, pageObj)?.subscribe(
      {
        next: (rolePageResponse: HttpResponse<KitManagementPageResponse>) => {
          if (rolePageResponse.status == 200) {
            this.paginateDataset(rolePageResponse.body);
          } else {
            this.kitResponseList = [];
            this.totalRecordDisplay = 0;
            this.totalRecord = 0;
            this.loading = false;
          }
          this.setLoadingStatus(false);
        }, error: (error: HttpErrorResponse) => {
          this.setLoadingStatus(false);
          this.exceptionService.customErrorMessage(error);
        }
      });
  }

  /**
   * List API Response set
   * 
   * @param kitManagementPageResponse 
   */
  private paginateDataset(kitManagementPageResponse: KitManagementPageResponse): void {
    this.totalItems = kitManagementPageResponse.totalElements;
    this.kitResponseList = kitManagementPageResponse.content;
    this.page = kitManagementPageResponse.number + 1;
    this.totalRecord = kitManagementPageResponse.totalElements;
    this.totalRecordDisplay = kitManagementPageResponse.numberOfElements;
    this.setLocalRoleId(this.kitResponseList);
    this.setLoadingStatus(false);
  }

  /**
   * Local kit Id list create for Select all Checkbox
   * 
   * <AUTHOR>
   * 
   * @param roleIdList 
   */
  public setLocalRoleId(kitList: KitManagementResponse[]): void {
    this.localKitIdListArray = [];
    for (let kitObj of kitList) {
      this.localKitIdListArray.push(kitObj.id);
    }
    this.defaultSelectAll();
  }

  /**
   * select All checkbox select or deSelect
   * 
   * <AUTHOR>
   */
  private defaultSelectAll(): void {
    this.commonCheckboxService.defaultSelectAll(this.localKitIdListArray, this.selectedKitIdList, this.selectAllCheckboxId);
  }

  /**
   * single Checkbox Select
   * 
   * <AUTHOR>
   * 
   * @param roleObj 
   * @param isChecked 
   */
  public selectCheckbox(kitManagementResponseObj: KitManagementResponse, isChecked: boolean): void {
    if (isChecked) {
      this.selectedKitIdList.push(kitManagementResponseObj.id);
    } else {
      var index = this.selectedKitIdList.findIndex(obj => obj == kitManagementResponseObj.id);
      this.selectedKitIdList.splice(index, 1);
    }
    this.defaultSelectAll();
  }

  /**
   * Select All CheckBox
   * 
   * <AUTHOR>
   * 
   * @param isChecked 
   */
  public selectAllItem(isChecked: boolean): void {
    this.selectedKitIdList = this.commonCheckboxService.selectAllItem(isChecked, this.localKitIdListArray, this.selectedKitIdList, this.checkboxListName);
  }

  /**
   * Set Loading status
   * 
   * <AUTHOR>
   * 
   * @param status 
   */
  private setLoadingStatus(status: boolean): void {
    this.loading = status;
    this.cdr.detectChanges();
  }

  /**
   * Show kit Detail
   * 
   * @param id 
   * 
   * <AUTHOR>
   */
  public showkitDetail(id: number): void {
    this.kitId = id;
    this.bridgeWorldListDisplay = false;
    this.bridgeWorldDetailDisplay = true;
  }

  /**
   * Show Kit List 
   * 
   * <AUTHOR>
   */
  public showKitList(): void {
    this.isFilterComponentInitWithApicall = false;
    this.listPageRefreshForbackToDetailPage = true;
    this.kitId = null;
    this.bridgeWorldListDisplay = true;
    this.bridgeWorldDetailDisplay = false;
    this.selectedKitIdList = [];
    if (this.isFilterHidden) {
      this.filterPageSubjectCallForReloadPage(true, false);
    }
  }

  /**
   * Show Ots World List Page
   * 
   * <AUTHOR>
   */
  public showOtsWorldListDisplay(): void {
    this.showOtsWorldList.emit();
  }

}
