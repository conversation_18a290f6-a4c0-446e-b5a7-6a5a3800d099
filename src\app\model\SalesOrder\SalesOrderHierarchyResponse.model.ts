import { ProductConfigStatus } from "src/app/shared/enum/SalesOrder/ProductConfigStatus.enum";
import { SalesOrderTypeStatus } from "src/app/shared/enum/SalesOrder/SalesOrderTypeStatus.enum";

export class SalesOrderHierarchyResponse {
    id: number;
    salesOrderNumber: string;
    orderType: SalesOrderTypeStatus;
    poNumber: string;
    soStatus: ProductConfigStatus;
    lastSyncDate: number;
    soCreatedDate: number;
    modifiedDate: number;
    salesforceOrder: boolean;

    constructor(
        id?: number, //NOSONAR
        salesOrderNumber?: string,
        orderType?: SalesOrderTypeStatus,
        poNumber?: string,
        soStatus?: ProductConfigStatus,
        lastSyncDate?: number,
        soCreatedDate?: number,
        modifiedDate?: number,
        salesforceOrder?: boolean
    ) {
        this.id = id;
        this.salesOrderNumber = salesOrderNumber;
        this.orderType = orderType;
        this.poNumber = poNumber;
        this.soStatus = soStatus;
        this.lastSyncDate = lastSyncDate;
        this.soCreatedDate = soCreatedDate;
        this.modifiedDate = modifiedDate;
        this.salesforceOrder = salesforceOrder;
    }
}