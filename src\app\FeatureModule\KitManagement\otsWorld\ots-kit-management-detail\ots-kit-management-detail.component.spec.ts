import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { INTERNAL_SERVER_ERROR, OTS_KIT_MANAGEMANT_DELETE } from 'src/app/app.constants';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { OTSKitManagementDetailResponse } from 'src/app/model/KitManagement/otsWorld/OTSKitManagementDetailResponse.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { OtsKitManagemantApiCallService } from 'src/app/shared/Service/KitManagemant/ots-kit-managemant-api-call.service';
import { OtsKitManagementDetailComponent } from './ots-kit-management-detail.component';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';

describe('OtsKitManagementDetailComponent', () => {
  let component: OtsKitManagementDetailComponent;
  let fixture: ComponentFixture<OtsKitManagementDetailComponent>;
  let exceptionHandlingService: ExceptionHandlingService;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let otsKitManagemantApiCallServiceSpy: jasmine.SpyObj<OtsKitManagemantApiCallService>;

  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    const confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['open', 'close']);
    const localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);
    otsKitManagemantApiCallServiceSpy = jasmine.createSpyObj('KitManagemantApiCallService', ['getOTSKitDetail']);

    // Mock the return value of retrieve for the getPermissionObject function
    localStorageServiceMock.retrieve.and.returnValue('mockedPermissionObject');
    await TestBed.configureTestingModule({
      declarations: [OtsKitManagementDetailComponent],
      imports: [],
      providers: [
        SessionStorageService,
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        { provide: OtsKitManagemantApiCallService, useValue: otsKitManagemantApiCallServiceSpy },
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(OtsKitManagementDetailComponent);
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call toastrService.error with INTERNAL_SERVER_ERROR', () => {
    // **Arrange: Mock dependencies and set up the test environment**
    // Create a mock HttpErrorResponse to simulate a server error
    const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });

    // Mock the API call to return an error response
    otsKitManagemantApiCallServiceSpy.getOTSKitDetail?.and.returnValue(throwError(() => mockError));

    // Spy on the `customErrorMessage` method of `exceptionHandlingService` to verify error handling
    spyOn(exceptionHandlingService, 'customErrorMessage')?.and.callThrough();

    // **Act: Trigger the component's logic**
    // Initialize the component, which invokes `getOTSKitDetail` internally
    component.ngOnInit();

    // **Assert: Verify error handling behavior**
    // Ensure the `customErrorMessage` method was called to process the error
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();

    // Ensure the toastr service displays an error message for `INTERNAL_SERVER_ERROR`
    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);
  });


  it('should call toastrService.info with OTS_KIT_MANAGEMANT_DELETE for empty response', () => {
    // **Arrange: Mock dependencies and set up the test environment**
    // Mock the API call to return an empty response with HTTP status 205
    otsKitManagemantApiCallServiceSpy.getOTSKitDetail?.and.returnValue(
      of(new HttpResponse<OTSKitManagementDetailResponse>({
        body: null, // Simulate an empty response body
        status: 205, // Mock HTTP status indicating a specific response
        statusText: 'OK',
      }))
    );

    // **Act: Trigger the component's logic**
    // Initialize the component, which invokes `getOTSKitDetail` internally
    component.ngOnInit();

    // **Assert: Verify the behavior for empty responses**
    // Ensure the toastr service displays an informational message for empty responses
    expect(toastrServiceMock.info).toHaveBeenCalledWith(OTS_KIT_MANAGEMANT_DELETE);
  });

});
