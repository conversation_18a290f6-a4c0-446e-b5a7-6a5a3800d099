import { Component, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { isNullOrUndefined, isUndefined } from 'is-what';
import { SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, SMALL_TEXTBOX_MAX_LENGTH, SPECIAL_CHARACTER_PATTERN, SPECIAL_CHARACTER_ERROR_MESSAGE, MEXIMUM_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, MAXIMUM_TEXTBOX_LENGTH } from 'src/app/app.constants';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { MultiSelectDropdownSettings } from 'src/app/model/MultiSelectDropdownSettings.model';
import { UploadFileChunckProperty } from 'src/app/model/SoftwaarBuilds/UploadFileChunckProperty.model';
import { UploadDialogBox } from 'src/app/model/UploadScanDialog';
import { UploadPackageRequest } from 'src/app/model/upload.package.request';
import { UploadPackageResponse } from 'src/app/model/upload.package.response';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { InventoryService } from 'src/app/shared/inventory.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { DownloadService } from 'src/app/shared/util/download.service';
import { MultiSelectDropDownSettingService } from 'src/app/shared/util/multi-select-drop-down-setting.service';
import { ValidationService } from 'src/app/shared/util/validation.service';



@Component({
  selector: 'app-upload-scan-dialog',
  templateUrl: './upload-scan-dialog.component.html',
  styleUrls: ['./upload-scan-dialog.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class UploadScanDialogComponent implements OnInit {

  @Input() title: string;
  @Input() message: string;
  @Input() btnOkText: string;
  @Input() btnCancelText: string;
  @Input() countryList: CountryListResponse[];
  @Input() jsonVersionList: any[];

  selectedFiles: FileList;
  titleText: string;
  version: string;
  disableBtn = true;
  fileValidation = false;
  fileSelectedState = false;

  //Text box max limit set
  textBoxMaxCharactersAllowedMessage: string = MEXIMUM_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  small_textBoxMaxCharactersAllowedMessage: string = SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  specialCharacterErrorMessage: string = SPECIAL_CHARACTER_ERROR_MESSAGE;
  //Progress bar
  uploadProgress: number = 0; // Upload percentage
  uploadedData: number = 0; // Uploaded data in MB
  totalData: number = 0; // Total file size in MB
  progressVisible: boolean = false;

  form = new FormGroup({
    Title: new FormControl('', [Validators.required, Validators.pattern('^[a-zA-Z0-9]+$'), Validators.maxLength(MAXIMUM_TEXTBOX_LENGTH)]),
    Version: new FormControl('', [Validators.required, this.commentservice.validateInput]),
    country: new FormControl([], Validators.required),
    jsonVersion: new FormControl([]),
    partNumber: new FormControl('', [Validators.required, Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH), this.validationService.removeSpaces, Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    fileSelector: new FormControl('', [Validators.required])
  });
  dropdownSettingsForJsonVersion: MultiSelectDropdownSettings = null;
  countryDropdownSettings: MultiSelectDropdownSettings = null;
  disabled = false;

  constructor(
    private activeModal: NgbActiveModal,
    private commonsService: CommonsService,
    private exceptionService: ExceptionHandlingService,
    private inventory_service: InventoryService,
    private downloadService: DownloadService,
    private multiSelectDropDownSettingService: MultiSelectDropDownSettingService,
    private validationService: ValidationService,
    private commentservice: CommonsService
  ) { }

  ngOnInit() {
    this.countryDropdownSettings = this.multiSelectDropDownSettingService.getCountryDrpSetting(false, true);
    this.dropdownSettingsForJsonVersion = this.multiSelectDropDownSettingService.getjsonVersionDropdownSetting(true);
    this.updateCountryList();
  }

  private updateCountryList(): void {
    if (!isNullOrUndefined(this.countryList)) {
      for (let index in this.countryList) {
        this.countryList[index].isDisabled = false;
      }
    }
  }

  public decline() {
    this.activeModal.close(false);
  }

  handleFileInput(event) {
    this.selectedFiles = event.target.files;
    this.form.controls['fileSelector'].setValue(event.target.files);
    if (this.selectedFiles.length == 2) {
      if ((this.selectedFiles[0].type == 'application/zip' || this.selectedFiles[0].type == 'application/x-zip-compressed') && this.selectedFiles[1].type == 'application/json'
        || (this.selectedFiles[1].type == 'application/zip' || this.selectedFiles[1].type == 'application/x-zip-compressed') && this.selectedFiles[0].type == 'application/json') {
        this.disableBtn = (!this.form.valid);
        this.fileValidation = false;
        this.fileSelectedState = true;
      }
      else {
        this.disableBtn = true;
        this.fileValidation = true;
        this.fileSelectedState = false;
      }
    }
    else {
      this.disableBtn = true;
      this.fileValidation = true;
      this.fileSelectedState = false;
    }

  }

  public buttonDisable(value): void {
    if (!isUndefined(this.selectedFiles)) {
      if (this.selectedFiles.length == 2) {
        setTimeout(() => {
          this.disableBtn = (!this.form.valid);
        }, 100);
        this.fileValidation = false;
      }
    }
  }


  public accept() {
    let datsetdialog = new UploadDialogBox(true, this.form.value.Title, this.form.value.Version, this.selectedFiles, this.commonsService.getIdsFromArray(this.form.value.country), this.form.value.jsonVersion,
      this.commonsService.checkNullFieldValue(this.form.value.partNumber));
    this.confirmDownloadDataset(datsetdialog)
  }

  private confirmDownloadDataset(uploadDialogBox: UploadDialogBox): void {
    let maxBlockSize = 100 * 1024 * 1024;//Each file will be split in 1 MB.
    let totalBytesRemaining = 0;
    this.progressVisible = true;
    this.disableBtn = true;

    let countryId = (isNullOrUndefined(uploadDialogBox.countryIds)) ? null : uploadDialogBox.countryIds;
    let jsonId = this.commonsService.getIdFromJsonObject((isNullOrUndefined(uploadDialogBox.jsonId)) ? null : uploadDialogBox.jsonId[0]);
    let attachmentFile: File = uploadDialogBox.selectedFiles.item(0);
    let releaseNoteFile: File = uploadDialogBox.selectedFiles.item(1);

    if (!attachmentFile.name.endsWith(".zip")) {
      let tempFile = attachmentFile;
      attachmentFile = releaseNoteFile;
      releaseNoteFile = tempFile;
    }

    let requestData = new UploadPackageRequest(uploadDialogBox.title, attachmentFile.name, releaseNoteFile.name, attachmentFile.size, countryId, jsonId, uploadDialogBox.partNumber);
    this.inventory_service.pushFileToStorage(requestData, {
      version: uploadDialogBox.version
    }).subscribe({
      next: (responseFromPushFileToStorage) => {
        this.inventory_service.uploadFileToStorage(releaseNoteFile, responseFromPushFileToStorage.body.preSignedUrlForReleaseNoteFile).subscribe();

        let fileSize = attachmentFile.size;
        if (fileSize < maxBlockSize) {
          maxBlockSize = fileSize;
        }

        totalBytesRemaining = fileSize;

        let currentFilePointer = 0;
        let blockIds = new Array();
        let blockIdPrefix = "block-";
        let uploadFileChunckProperty = new UploadFileChunckProperty(totalBytesRemaining, currentFilePointer, maxBlockSize, attachmentFile, blockIdPrefix, blockIds);
        this.uploadFileInChunk(uploadFileChunckProperty, responseFromPushFileToStorage.body, uploadDialogBox.version, requestData);
      },
      error: (error) => {
        this.disableBtn = false;
        this.progressVisible = false;
        this.setLoadingStatus(false);
        this.exceptionService.customErrorMessage(error);
      }
    });
  }

  /**
 * Encode string convert into base64 string
 * <AUTHOR>
 */
  private encodeToBase64(str: string): string {
    const uint8Array = new TextEncoder().encode(str); // Encode to UTF-8 bytes
    let binaryString = '';

    // Convert each byte in the Uint8Array to a character
    uint8Array.forEach(byte => {
      binaryString += String.fromCharCode(byte);
    });

    // Use a custom Base64 encoding method
    return window.btoa(binaryString); // You can use a custom base64 encoding logic here if preferred
  }

  public uploadFileInChunk(uploadFileChunckProperty: UploadFileChunckProperty, uploadPackageResponse: UploadPackageResponse, versionOfFile: string, requestData: UploadPackageRequest) {
    const totalFileSize = uploadFileChunckProperty.attachmentFile.size;
    this.totalData = totalFileSize; // Initialize the total size
    if (uploadFileChunckProperty.totalBytesRemaining > 0) {
      const bytesUploaded = totalFileSize - uploadFileChunckProperty.totalBytesRemaining;
      this.uploadedData = bytesUploaded;
      this.uploadProgress = (bytesUploaded / totalFileSize) * 100;

      let fileContent: any = uploadFileChunckProperty.attachmentFile.slice(uploadFileChunckProperty.currentFilePointer, uploadFileChunckProperty.currentFilePointer + uploadFileChunckProperty.maxBlockSize);
      let blockId = uploadFileChunckProperty.blockIdPrefix + this.pad(uploadFileChunckProperty.blockIds.length, 6);
      uploadFileChunckProperty.blockIds.push(this.encodeToBase64(blockId));
      let uri = uploadPackageResponse.preSignedUrlForAttachmentFile + '&comp=block&blockid=' + uploadFileChunckProperty.blockIds[uploadFileChunckProperty.blockIds.length - 1];
      this.inventory_service.uploadFileToStorage(fileContent, uri).subscribe(() => {
        let uploadFileChunck = new UploadFileChunckProperty(uploadFileChunckProperty.totalBytesRemaining, uploadFileChunckProperty.currentFilePointer, uploadFileChunckProperty.maxBlockSize, uploadFileChunckProperty.attachmentFile, uploadFileChunckProperty.blockIdPrefix, uploadFileChunckProperty.blockIds);
        this.uploadFileInChunk(uploadFileChunck, uploadPackageResponse, versionOfFile, requestData);
      });

      uploadFileChunckProperty.currentFilePointer += uploadFileChunckProperty.maxBlockSize;
      uploadFileChunckProperty.totalBytesRemaining -= uploadFileChunckProperty.maxBlockSize;

      if (uploadFileChunckProperty.totalBytesRemaining < uploadFileChunckProperty.maxBlockSize) {
        uploadFileChunckProperty.maxBlockSize = uploadFileChunckProperty.totalBytesRemaining;
      }
    } else {
      // Commit the blocks and finalize the upload
      let uriPath = uploadPackageResponse.preSignedUrlForAttachmentFile + '&comp=blocklist';
      let requestBody = '<?xml version="1.0" encoding="utf-8"?><BlockList>';
      for (let blockId of uploadFileChunckProperty.blockIds) {
        requestBody += `<Latest>${blockId}</Latest>`;
      }
      requestBody += '</BlockList>';
      this.inventory_service.commitFileToStorage(requestBody, uriPath, uploadFileChunckProperty.attachmentFile.type).subscribe({
        next: (response) => {
          if (response.status == 201) {
            this.inventory_service.updateFirmwareUploadStatus(requestData, {
              version: versionOfFile
            }).subscribe({
              next: () => {
                this.activeModal.close(true);
              }, error: (error) => {
                this.setLoadingStatus(false);
                this.exceptionService.customErrorMessage(error);
              }
            });
          }
        }
      });
    }
  }

  private pad(number, length) {
    let str = '' + number;
    while (str.length < length) {
      str = '0' + str;
    }
    return str;
  }

  private setLoadingStatus(status: boolean) {
    this.downloadService.setLoading(status, null);
  }

}


