import { HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { FormControl, FormGroup, ValidatorFn } from '@angular/forms';
import { isNullOrUndefined } from 'is-what';
import { throwError } from 'rxjs';
import { CLIENT_DEVICE, DEMO_DEVICE, JSON_VERSION_PATTERN, NotAssociated, SoftwareBuild_Map_ClientAndDemoDevice, SoftwareBuild_Map_ClientDevice, SoftwareBuild_Map_DemoDevice, TEST_DEVICE, VERSION_PATTERN, notAssociated } from 'src/app/app.constants';
import { MultiSelectDropDownRequest } from 'src/app/model/MultiSelectDropDownRequest.model';
import { BooleanKeyValueMapping } from 'src/app/model/common/BooleanKeyValueMapping.model';
import { EnumMapping } from 'src/app/model/common/EnumMapping.model';
import { MultiSelectDropdownSetValue } from 'src/app/model/common/MultiSelectDropdownSetValue.model';
import { ProbeFeatureResponse } from 'src/app/model/probe/ProbeFeatureResponse.model';
import { ProbePresetResponse } from 'src/app/model/probe/ProbePresetResponse.model';
import { DataSizeEnum } from '../enum/DataSizeEnum.enum';
import { ValidityEnum } from '../enum/ValidityEnum.enum';
import { deviceTypesEnum } from '../enum/deviceTypesEnum.enum';
import { EndDateOptions } from '../enum/endDateOptions.enum';
import { inventoryStatusEnum } from '../enum/inventoryStatusEnum';
import { MultiSelectDropDownRequestForCountryModel } from 'src/app/model/multi-select-drop-down-request-for-country.model';

@Injectable({
  providedIn: 'root'
})
export class CommonsService {

  public checkForNull(aray: Array<any>) {
    if (!isNullOrUndefined(aray) && aray?.length > 0) {
      return aray.filter(value => value != null && value != "");
    }
    return [];
  }

  public getIdFromJsonObject(json: any): number {
    return (isNullOrUndefined(json)) ? null : json.id;
  }

  public getIdsFromArray(list: any[]): number[] | null {
    let listIds = [];
    if (list != null && list?.length > 0) {
      list.forEach(element => {
        listIds.push(element.id);
      });
    } else {
      listIds = null;
    }
    return listIds;
  }

  /**
   * Get Drowndown value for selection
   * @param list 
   * @param idList 
   * @returns 
   */
  public getDropDownValue(list: Array<any>, idList: Array<number>): Array<MultiSelectDropdownSetValue> {
    let selectedValue: Array<MultiSelectDropdownSetValue> = [];
    if (list?.length > 0 && idList != null && idList?.length > 0) {
      let filter = list.filter(obj => idList.includes(obj.id));
      for (let listObject of filter) {
        selectedValue.push(new MultiSelectDropdownSetValue(listObject.id, listObject.country));
      }
    }
    return selectedValue;
  }

  /**
  * Get Drowndown value for selection by DisplayName
  * @param list 
  * @param countryList 
  * @returns 
  */
  public getDropDownValueByName(list: Array<any>, countryList: Array<string>): Array<MultiSelectDropdownSetValue> {
    let selectedValue: Array<MultiSelectDropdownSetValue> = [];
    if (list?.length > 0 && countryList != null && countryList?.length > 0) {
      let filter = list.filter(obj => countryList.includes(obj.country));
      for (let listObject of filter) {
        selectedValue.push(new MultiSelectDropdownSetValue(listObject.id, listObject.country));
      }
    }
    return selectedValue;
  }

  public getValuesFromArray(filterForm: FormGroup, field: string) {
    let value = (isNullOrUndefined(filterForm.get(field).value) || filterForm.get(field).value == "") ? null : filterForm.get(field).value;
    if (field == 'inventoryStatus' && !isNullOrUndefined(value)) {
      value = (value == inventoryStatusEnum.ACTIVE);
    }
    return value;
  }

  public getDeviceTypeFilterValueArray(filterForm: FormGroup, field: string) {
    let formValue = filterForm.get(field).value;
    if (isNullOrUndefined(formValue) || (typeof formValue === "object" && formValue?.length == 0) || formValue == "") {
      return null;
    } else {
      return (formValue == deviceTypesEnum.ABOVE_BOTH) ? this.getDeviceTypeStringToEnum(filterForm.get(field).value) : [this.getDeviceTypeStringToEnum(filterForm.get(field).value)];
    }
  }

  /**
   * Device Type DropDown Selection Value
   * 
   * @param deviceTypes 
   * @returns 
   */
  public getDeviceTypeDropDownValueSelection(deviceTypes: Array<string>): Array<string> {
    let selectedDeviceType = [];
    if (!isNullOrUndefined(deviceTypes) && deviceTypes?.length > 0) {
      if (deviceTypes.includes(deviceTypesEnum.CLIENT_DEVICE) && deviceTypes.includes(deviceTypesEnum.DEMO_DEVICE)) {
        selectedDeviceType.push(deviceTypesEnum.ABOVE_BOTH);
      } else if (deviceTypes.includes(deviceTypesEnum.CLIENT_DEVICE)) {
        selectedDeviceType.push(CLIENT_DEVICE);
      } else if (deviceTypes.includes(deviceTypesEnum.DEMO_DEVICE)) {
        selectedDeviceType.push(DEMO_DEVICE);
      }
    }
    return selectedDeviceType;
  }

  public getDeviceTypeMapMessage(deviceType: deviceTypesEnum[]): string {
    if (deviceType?.length == 1 && deviceType[0] == deviceTypesEnum.CLIENT_DEVICE) {
      return SoftwareBuild_Map_ClientDevice;
    } else if (deviceType?.length == 1 && deviceType[0] == deviceTypesEnum.DEMO_DEVICE) {
      return SoftwareBuild_Map_DemoDevice;
    } else {
      return SoftwareBuild_Map_ClientAndDemoDevice;
    }
  }

  public getDeviceTypeStringToEnum(value: string): any {
    if (value == CLIENT_DEVICE) {
      return deviceTypesEnum.CLIENT_DEVICE;
    } else if (value == DEMO_DEVICE) {
      return deviceTypesEnum.DEMO_DEVICE;
    } else if (value == TEST_DEVICE) {
      return deviceTypesEnum.TEST_DEVICE;
    } else if (value == deviceTypesEnum.ABOVE_BOTH) {
      return [deviceTypesEnum.CLIENT_DEVICE, deviceTypesEnum.DEMO_DEVICE];
    } else if (value == NotAssociated) {
      return deviceTypesEnum.NOT_ASSOCIATED;
    } else {
      return null;
    }
  }

  public getDeviceTypeEnumToString(deviceType: deviceTypesEnum): string {
    if (deviceType == deviceTypesEnum.CLIENT_DEVICE) {
      return CLIENT_DEVICE;
    } else if (deviceType == deviceTypesEnum.DEMO_DEVICE) {
      return DEMO_DEVICE;
    } else if (deviceType == deviceTypesEnum.TEST_DEVICE) {
      return TEST_DEVICE;
    } else {
      return null;
    }
  }

  // get Values from any Enum in key value pair
  public getEnumToList(enumName: any): EnumMapping[] {
    let keyValueList = [];
    for (let enumObject in enumName) {
      let enumMappingObj = new EnumMapping(enumObject, enumName[enumObject]);
      keyValueList.push(enumMappingObj);
    }
    return keyValueList;
  }

  /**
   * Filter EnumMapping List
   * 
   * <AUTHOR>
   * @param enumMapping 
   * @param removeValueList 
   * @returns 
   */
  public getEnumMappingListFilter(enumMapping: EnumMapping[], removeValueList: Array<string>): EnumMapping[] {
    return enumMapping.filter(enumObj => !removeValueList.includes(enumObj.value));
  }

  /**
   * Get Enum Selected value
   * 
   * Filter value pass only name but multiselect dropdown required key and value
   * 
   * <AUTHOR>
   * 
   * @param enumName 
   * @param enumValue 
   * @returns 
   */
  public getEnumMappingSelectedValue(enumName: any, enumValue: Array<string>): EnumMapping[] {
    let keyValueList = [];
    if (!isNullOrUndefined(enumValue)) {
      for (let enumObject in enumName) {
        if (enumValue.includes(enumObject)) {
          let enumMappingObj = new EnumMapping(enumObject, enumName[enumObject]);
          keyValueList.push(enumMappingObj);
        }
      }
    }
    return keyValueList;
  }

  /**
   * Get key from Enum 
   *  
   * @param enumMapping 
   * @returns 
   */
  public getSelectedValueFromEnum(enumMapping: Array<EnumMapping>) {
    let valueList = [];
    if (!isNullOrUndefined(enumMapping)) {
      for (let enumMappingObject of enumMapping) {
        valueList.push(enumMappingObject.key);
      }
    }
    return valueList;
  }

  /**
   * Get Selected Value From  Boolean Key Value Mapping
   * @param enumMapping 
   * @returns 
   */
  public getSelectedValueFromBooleanKeyValueMapping(enumMapping: Array<BooleanKeyValueMapping>): boolean {
    if (!isNullOrUndefined(enumMapping) && enumMapping?.length == 1) {
      return enumMapping[0].value;
    }
    return null;
  }


  /**
   * Set End Time of the Day
   * @returns 
   */
  public getEndTimeOfDay(date: Date): Date {
    return new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59);
  }

  /**
   * Gives last day of month(pass month + 1 and day = 0)
   * Grace periad added of one month(pass month + 2)
   * @returns 
   */
  private getLastDayofMonthAndYearAdd(year: number): Date {
    return new Date(new Date(new Date().getFullYear() + year, new Date().getMonth() + 2, 0, 23, 59, 59).toString());
  }

  /**
   * Get End Date Option base On part number
   *  
   * <AUTHOR>
   * 
   * @param partNumber 
   */
  public getEndDateOptions(validity: ValidityEnum): EndDateOptions {
    if (!isNullOrUndefined(validity)) {
      if (ValidityEnum[validity] == ValidityEnum.PERPETUAL) {
        return EndDateOptions.UNLIMITED;
      } else if (ValidityEnum[validity] == ValidityEnum.ONE_YEAR) {
        return EndDateOptions.ONE_YEAR;
      }
    }
    return null;
  }

  /**
   * Set Default feature value unlimited or 12 month based on enable Feature
   * 
   * @param featuresBaseResponse 
   * @returns 
   */
  public getFeatureDefalutValidty(featuresBaseResponse: ProbeFeatureResponse | ProbePresetResponse): EndDateOptions {
    if (!isNullOrUndefined(featuresBaseResponse) &&
      !isNullOrUndefined(featuresBaseResponse.partNumbers) &&
      featuresBaseResponse.partNumbers?.length > 0) {
      let index = featuresBaseResponse.partNumbers.findIndex(obj => ValidityEnum[obj.validity] == ValidityEnum.PERPETUAL);
      return index != -1 ? EndDateOptions.UNLIMITED : EndDateOptions.ONE_YEAR;
    }
    return EndDateOptions.UNLIMITED;
  }

  /**
   * End Date selection of feature
   * @param yearOption 
   * @param endDate 
   * @returns 
   */
  public getEndDateConvertion(endDateOption: EndDateOptions, customEndDate: Date): number {
    switch (endDateOption) {
      case EndDateOptions.ONE_YEAR:
        let currentDate: Date = this.getLastDayofMonthAndYearAdd(1);
        return this.getUTCValueFromDateWithPeriod(currentDate);
      case EndDateOptions.UNLIMITED:
        return -1;
      case EndDateOptions.CUSTOMDATE:
        let finalCustomDate = isNullOrUndefined(customEndDate) ? new Date() : customEndDate;
        return this.getUTCValueFromDate(finalCustomDate);
      default:
        return this.getUTCValueFromDate(new Date());
    }
  }

  /**
   * Get UTC Time for custom date
   * 
   * <AUTHOR>
   * @param dobValue 
   * @returns 
   */
  public getUTCValueFromDate(dobValue: Date): number {
    let utcDate = Date.UTC(dobValue.getFullYear(), dobValue.getMonth(), dobValue.getDate(), 23, 59, 59);
    let utc_dobValue = new Date(utcDate);
    return utc_dobValue.getTime();
  }

  /**
   * Get UTC Time
   * 
   * <AUTHOR>
   * @param dobValue 
   * @param yearCount 
   * @returns 
   */
  private getUTCValueFromDateWithPeriod(dobValue: Date): number {
    let utcDate = Date.UTC(dobValue.getFullYear(), dobValue.getMonth(), dobValue.getDate(), dobValue.getHours(), dobValue.getMinutes(), dobValue.getSeconds());
    let utc_dobValue = new Date(utcDate);
    return utc_dobValue.getTime();
  }

  /**
   * Utc Date Display
   * 
   * <AUTHOR>
   * @param epochDate 
   * @returns 
   */
  public getUTCDateForDisplay(epochDate: number): Date {
    let dobValue = new Date(epochDate);
    return new Date(dobValue.getUTCFullYear(), dobValue.getUTCMonth(), dobValue.getUTCDate(), dobValue.getUTCHours(), dobValue.getUTCMinutes(), dobValue.getUTCSeconds());
  }

  public notAssociatedObject(): MultiSelectDropDownRequest {
    return new MultiSelectDropDownRequest(-1, notAssociated, NotAssociated, false);
  }

  public notAssociatedObjectForCountry(): MultiSelectDropDownRequestForCountryModel {
    return new MultiSelectDropDownRequestForCountryModel(-1, notAssociated, NotAssociated, false);
  }

  public accessDataSizes(): string[] {
    let dataSizes: string[] = [];
    for (let dataSize in DataSizeEnum) {
      dataSizes.push(DataSizeEnum[dataSize]);
    }
    return dataSizes;
  }

  /**
  * Validation for White space
  * @returns 
  */
  public removeSpacesThrowError(): ValidatorFn {
    return function validate(control: FormControl) {
      if (control && control.value && (typeof control.value == "string" && !control.value.replace(/\s/g, '')?.length)) {
        control.setValue('');
        return { whiteSpace: true };
      }
      return null;
    };
  }


  /**
  * check for null value of field
  */
  public checkNullFieldValue(fieldValue: string | any): string | any | null {
    if ((typeof fieldValue == "string" && fieldValue.trim() == "") || isNullOrUndefined(fieldValue)) {
      return null;
    }
    return (typeof fieldValue == "string") ? fieldValue.trim() : fieldValue;
  }

  public checkValueIsNullOrEmpty(value: any) {
    if (isNullOrUndefined(value) || (typeof value == "string" && value.trim() == "") ||
      (Array.isArray(value)) && value?.length == 0) {
      return true;
    }
    return false;
  }

  /**
  * This Function pass enum and current value return enum key  
  * @param enumList 
  * @param curretValue 
  * @returns 
  */
  public getEnumKey(enumList: any, curretValue: string): any {
    for (let obj in enumList) {
      if (curretValue == enumList[obj]) {
        return obj;
      }
    }
    return null;
  }

  /**
  * throw Error 
  * 
  * <AUTHOR>
  * @param error 
  * @returns 
  */
  public handleError(error: HttpErrorResponse) {
    return throwError(() => new HttpErrorResponse(error));
  }

  /**
  * Helper function to escape potentially unsafe HTML characters
  * 
  * <AUTHOR>
  * @param text 
  * @returns
  */
  public escapeHtml(text: string): string {
    // Escape HTML to prevent potential XSS
    return text.replace(/[&<>"']/g, function (match) {
      const escape = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
      };
      return escape[match];
    });
  }


  /**
  * Validates the input value against the VERSION_PATTERN.
  * 
  * @param {FormControl} c - The form control whose value needs to be validated.
  * @returns {null | object} - Returns `null` if the value matches the pattern (valid input),
  *                            otherwise returns an object indicating the validation failure.
  */
  public validateInput(c: FormControl) {
    return (VERSION_PATTERN.test(c.value)) ? null : {
      validateInput: {
        valid: false
      }
    };
  }

  /**
  * Validates the input value against the JSON_VERSION_PATTERN.
  * 
  * @param {FormControl} c 
  * @returns {null | object}
  */
  public validateJsonInput(c: FormControl) {
    return (JSON_VERSION_PATTERN.test(c.value)) ? null : {
      validateInput: {
        valid: false
      }
    };
  }

}
