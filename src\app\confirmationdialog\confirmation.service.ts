import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { Cancel, Confirm, ProbDetailResource } from '../app.constants';
import { BasicModelConfig } from '../model/common/BasicModelConfig.model';
import { ConfirmationDialogComponent } from './confirmation-dialog.component';

@Injectable()
export class ConfirmDialogService {

  constructor(
    private modalService: NgbModal,
    private toastrService: ToastrService,
  ) { }

  public confirm(
    title: string,
    message: string,
    btnOkText: string = 'OK',
    btnCancelText: string = 'Cancel',
    infoMessagePrefix: string = null,
    infoMessage: string = null,
    dialogSize = 'sm'): Promise<boolean> {
    const modalRef = this.modalService.open(ConfirmationDialogComponent, { size: dialogSize, windowClass: "modal fade modal-dimmed", backdropClass: "modal-dimmed" });
    modalRef.componentInstance.title = title;
    modalRef.componentInstance.message = message;
    modalRef.componentInstance.btnOkText = btnOkText;
    modalRef.componentInstance.btnCancelText = btnCancelText;
    modalRef.componentInstance.infoMessage = infoMessage;
    modalRef.componentInstance.infoMessagePrefix = infoMessagePrefix;
    return modalRef.result;

  }

  /**
   * Get Model Config For Disable Action
   * <AUTHOR>
   * @param moduleName 
   * @returns 
   */
  public getBasicModelConfigForDisableAction(resource: string): BasicModelConfig {
    let message = "Are you sure you want to update the " + this.getModuleName(resource) + " as disable?";
    return this.prepareBasicModelConfigResponceForProductStatus(message);
  }

  /**
   * Get Model Config For RMA Action
   * <AUTHOR>
   * @param moduleName 
   * @returns 
   */
  public getBasicModelConfigForRMAAction(resource: string): BasicModelConfig {
    let message = "Are you sure you want to update the " + this.getModuleName(resource) + " as RMA?";
    return this.prepareBasicModelConfigResponceForProductStatus(message);
  }

  /**
   * Prepare Model Config For Action
   * 
   * <AUTHOR>
   * @param message 
   * @returns 
   */
  private prepareBasicModelConfigResponceForProductStatus(message: string): BasicModelConfig {
    return new BasicModelConfig("Are you sure?", message, Confirm, Cancel);
  }

  private getModuleName(resource: string) {
    return (resource == ProbDetailResource) ? 'Probe' : 'Device';
  }

  /**
  * Generates an error message when attempting to disable a resource 
  * that is either already marked as RMA or cannot be updated as RMA.
  * 
  * <AUTHOR>
  * @param {string} resource - The name of the module or resource being checked.
  */
  public getErrorMessageDisableToRma(resource: string): void {
    this.toastrService.info(`Disable ${this.getModuleName(resource)} is not allowed to update as RMA or it is already marked as RMA.`);
  }


}