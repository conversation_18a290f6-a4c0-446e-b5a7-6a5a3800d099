import { ComponentFixture, TestBed } from '@angular/core/testing';

import { InventoryOperationsComponent } from './inventory-operations.component';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

describe('InventoryOperationsComponent', () => {
  let component: InventoryOperationsComponent;
  let fixture: ComponentFixture<InventoryOperationsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [InventoryOperationsComponent],
      providers: [NgbActiveModal]
    })
      .compileComponents();

    fixture = TestBed.createComponent(InventoryOperationsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
