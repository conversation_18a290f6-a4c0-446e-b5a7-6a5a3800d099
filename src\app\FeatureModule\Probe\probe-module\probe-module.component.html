<!------------------------------------------------------------->
<!----------- Probe Listing Page Start ------------------------->
<!------------------------------------------------------------->
<ng-template [ngIf]="isProbeListingPageDisplay">
    <app-ots-probes (showProbeDetail)="showProbeDetail($event)"
        [probeListFilterRequestBody]="probeListFilterRequestBody"
        (probeListFilterRequestBodyChange)="updateProbeListFilterRequestBody($event)" [isFilterHidden]="isFilterHidden"
        (isFilterHiddenChange)="updateIsFilterHidden($event)"></app-ots-probes>
</ng-template>
<!------------------------------------------------------------>
<!--------- Probe Listing Page  End-------------------------->
<!------------------------------------------------------------>

<!------------------------------------------------------------->
<!----------- Probe Detail Page Start ------------------------->
<!------------------------------------------------------------->
<ng-template [ngIf]="isProbeDetailPageDisplay">
    <app-ots-probes-detail [probeId]="probeIdInput" (showProbe)="showProbe()"></app-ots-probes-detail>
</ng-template>
<!------------------------------------------------------------>
<!--------- Probe Detail Page  End-------------------------->
<!------------------------------------------------------------>