import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UpdateAssociationComponent } from './update-association.component';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

describe('UpdateAssociationComponent', () => {
  let component: UpdateAssociationComponent;
  let fixture: ComponentFixture<UpdateAssociationComponent>;
  let mockActiveModal: jasmine.SpyObj<NgbActiveModal>;

  beforeEach(async () => {
    const spy = jasmine.createSpyObj('NgbActiveModal', ['close', 'dismiss']);

    await TestBed.configureTestingModule({
      declarations: [UpdateAssociationComponent],
      providers: [
        { provide: NgbActiveModal, useValue: spy }
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(UpdateAssociationComponent);
    component = fixture.componentInstance;
    mockActiveModal = TestBed.inject(NgbActiveModal) as jasmine.SpyObj<NgbActiveModal>;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call activeModal.close(false) when decline() is called', () => {
    component.decline();
    expect(mockActiveModal.close).toHaveBeenCalledWith(false);
  });

  it('should call activeModal.close(true) when accept() is called', () => {
    component.accept();
    expect(mockActiveModal.close).toHaveBeenCalledWith(true);
  });

  it('should call activeModal.dismiss() when dismiss() is called', () => {
    component.dismiss();
    expect(mockActiveModal.dismiss).toHaveBeenCalledWith();
  });
});
