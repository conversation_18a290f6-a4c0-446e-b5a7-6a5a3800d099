import { Component } from '@angular/core';
import { ProbListResource } from 'src/app/app.constants';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { ProbeListFilterRequestBody } from 'src/app/model/probe/ProbeListFilterRequestBody.model';
import { ProbeOperationService } from '../ProbeService/Probe-Operation/probe-operation.service';

@Component({
  selector: 'app-probe-module',
  templateUrl: './probe-module.component.html',
  styleUrl: './probe-module.component.css'
})
export class ProbeModuleComponent {

  probeListFilterRequestBody: ProbeListFilterRequestBody = null;
  isFilterHidden: boolean = false;

  isProbeListingPageDisplay: boolean = true;
  isProbeDetailPageDisplay: boolean = false;

  loading: boolean = false;

  probeIdInput: number;
  probeListResource = ProbListResource;

  // Properties for filter component initialization
  isFilterComponentInitWithApicall: boolean = true;
  listPageRefreshForbackToDetailPage: boolean = false;

  constructor(private readonly probeOperationService: ProbeOperationService) { }

  /**
    * Call Filter component subject and reload page
    * <AUTHOR>
    * @param isDefaultPageNumber
    * @param isClearFilter
    */
  public filterPageSubjectCallForReloadPage(isDefaultPageNumber: boolean, isClearFilter: boolean): void {
    let listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, isDefaultPageNumber, isClearFilter, false);
    this.probeOperationService.callRefreshPageSubject(listingPageReloadSubjectParameter, ProbListResource, this.isFilterHidden, this.probeListFilterRequestBody);
  }

  /**
  * Update probeListFilterRequestBody from child component
  * <AUTHOR>
  * @param value
  */
  public updateProbeListFilterRequestBody(value: ProbeListFilterRequestBody): void {
    this.probeListFilterRequestBody = value;
  }

  /**
  * Update isFilterHidden from child component
  * <AUTHOR>
  * @param value
  */
  public updateIsFilterHidden(value: boolean): void {
    this.isFilterHidden = value;
  }

  public showProbeDetail(probeId: number) {
    this.probeIdInput = probeId;
    this.isProbeDetailPageDisplay = true;
    this.isProbeListingPageDisplay = false;
  }

  /**
  * Show probe listing page from detail page
  * <AUTHOR>
  */
  public showProbe(): void {
    this.isProbeListingPageDisplay = true;
    this.isProbeDetailPageDisplay = false;
    this.filterPageSubjectCallForReloadPage(false, false);
  }

  public getProbeId(probeId: number): void {
    this.probeIdInput = probeId;
  }

}
