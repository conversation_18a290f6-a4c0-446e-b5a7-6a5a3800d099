import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, EventEmitter, Input, OnInit, Output, ViewEncapsulation } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { ToastrService } from 'ngx-toastr';
import { TransferOrderResource } from 'src/app/app.constants';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { ConfigMappingRequest } from 'src/app/model/probe/ConfigMappingRequest.model';
import { ConfigureLicenceDetails } from 'src/app/model/probe/ConfigureLicenceDetails.model';
import { ConfigureLicenceResponse } from 'src/app/model/probe/ConfigureLicenceResponse.model';
import { SalesOrderTransferSuccessResponse } from 'src/app/model/SalesOrder/SalesOrderTransferSuccessResponse.model';
import { TransferOrderProductReviewResponse } from 'src/app/model/SalesOrder/TransferOrderProductReviewResponse.model';
import { SalesOrderTransferValidateRequest } from 'src/app/model/SalesOrder/SalesOrderTransferValidateRequest.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { UpdateFeaturesService } from 'src/app/shared/modalservice/update-features.service';
import { SalesOrderApiCallService } from 'src/app/shared/Service/SalesOrderService/sales-order-api-call.service';
import { SalesOrderTransferProbeDetailValidateRequest } from 'src/app/model/SalesOrder/SalesOrderTransferProbeDetailValidateRequest.model';
import { SalesOrderTransferProductDetailValidateRequest } from 'src/app/model/SalesOrder/SalesOrderTransferProductDetailValidateRequest.model';
import { SalesOrderTransferProductValidateRequest } from 'src/app/model/SalesOrder/SalesOrderTransferProductValidateRequest.model';
import { SourceSelectedProbeAndDevice } from 'src/app/model/SalesOrder/SourceSelectedProbeAndDevice.model';
import { TransferOrderService } from 'src/app/shared/Service/TransferOrderService/transfer-order.service';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { PermissionService } from 'src/app/shared/permission.service';

@Component({
  selector: 'app-transfer-order-review',
  templateUrl: './transfer-order-review.component.html',
  styleUrl: './transfer-order-review.component.css',
  encapsulation: ViewEncapsulation.None
})
export class TransferOrderReviewComponent implements OnInit {
  @Input("reviewOrderRequest") reviewOrderRequest: SalesOrderTransferValidateRequest;
  @Input("selectedProbeAndDevice") selectedProbeAndDevice: SourceSelectedProbeAndDevice;
  @Input("isSalesOrderManual") isSalesOrderManual: boolean;
  @Output("hideAndShowTable") hideAndShowTable = new EventEmitter;
  @Output("backToDetailPage") backToDetailPage = new EventEmitter;

  bothSalesOrderManual: boolean = false;
  serialNumber: string;

  constructor(
    private readonly dialogService: ConfirmDialogService,
    private readonly salesOrderApiCallService: SalesOrderApiCallService,
    private readonly toastr: ToastrService,
    private readonly exceptionService: ExceptionHandlingService,
    private readonly updateFeaturesService: UpdateFeaturesService,
    private readonly transferOrderService: TransferOrderService,
    private readonly permissionService: PermissionService,
  ) { }

  loading = false;
  transferOrderData: TransferOrderProductReviewResponse;
  warningTextColor = 'text-primary';
  errorTextColor = 'text-danger';

  transferOrderReviewDisplay: boolean = false;
  probeDetailPageDiplay: boolean = false;
  deviceDetailPageDiplay: boolean = false;
  probeReadOnlyPermission: boolean = false;
  deviceReadOnlyPermission: boolean = false;

  //EntityId for Probe or Device
  probeEntityId: number = null;
  bridgeEntityValue: number = null;
  transferOrderResource: string = TransferOrderResource;

  /**
  * <AUTHOR>
  * Lifecycle hook that runs after component initialization.
  * Assigns the input review order response to local variable.
  */
  ngOnInit(): void {
    this.loading = true;
    this.transferOrderReviewDisplay = true;
    this.probeReadOnlyPermission = this.permissionService.getProbPermission(PermissionAction.GET_PROB_ACTION);
    this.deviceReadOnlyPermission = this.permissionService.getDevicePermission(PermissionAction.GET_DEVICE_ACTION);
    this.transferOrderService.submitTranferOrderDetails(this.reviewOrderRequest).then((data) => {
      this.transferOrderData = data;
      this.bothSalesOrderManual = this.transferOrderData?.sourceSalesOrderIsManual && this.transferOrderData?.destinationSalesOrderIsManual;
      const serial = this.selectedProbeAndDevice?.sourceDeviceSelected?.serialNumber ?? this.selectedProbeAndDevice?.sourceProbeSelected?.serialNumber;
      this.serialNumber = serial ? `- ${serial}` : '';
      this.loading = false;
    });
  }

  /**
  * <AUTHOR>
  * Handles the transfer order action.
  * If there is a warning, prompts the user for confirmation.
  * @param warning - Boolean flag to indicate if there is a warning.
  */
  transferOrder(warning: boolean): void {
    if (warning) {
      this.dialogService
        .confirm('Transfer', `One or more products and countries do not match with orders ${this.transferOrderData?.sourceSalesOrder?.salesOrderNumber} and ${this.transferOrderData?.destinationSalesOrder?.salesOrderNumber}. Are you sure you want to proceed with the transfer?`)
        .then((confirmed) => {
          if (confirmed) {
            this.transferProduct();
          }
        });
    } else {
      this.transferProduct();
    }
  }

  /**
  * <AUTHOR>
  * Emits event to hide and show table.
  */
  back(): void {
    if (this.isSalesOrderManual) {
      this.hideAndShowTable.emit([true, false, false]);
    } else {
      this.hideAndShowTable.emit([false, true, false]);
    }
  }

  /**
  * <AUTHOR>
  * Initiates the product transfer process by calling API.
  * Displays success or error messages accordingly.
  */
  private transferProduct(): void {
    this.loading = true;
    this.salesOrderApiCallService.TransferSalesOrder(this.prepareTransferOrderRequest())?.subscribe({
      next: (res: HttpResponse<SalesOrderTransferSuccessResponse>) => {
        if (isNullOrUndefined(res.body?.message)) {
          this.transferOrderData = res.body.salesOrderTransferValidateResponse;
          if (res.body.salesOrderTransferValidateResponse.errorMessages?.length > 0) {
            this.toastr.error(res.body.salesOrderTransferValidateResponse.errorMessages[0]);
            this.backToDetailPage.emit();
          }
        } else {
          this.toastr.success(res.body?.message);
          this.backToDetailPage.emit();
        }
        this.loading = false;
      },
      error: (error: HttpErrorResponse) => {
        this.loading = false;
        this.exceptionService.customErrorMessage(error);
      }
    });
  }

  /**
  * Update probe features
  */
  public updateProbeFeatures(configMappingRequest: ConfigMappingRequest, type: string, index: number): void {
    let configureLicenceDetails = new ConfigureLicenceDetails(null, type, configMappingRequest);
    this.updateFeaturesService.openAssignProbeFeatureModel(this.updateFeaturesService.getAssignProbeBasicModelConfigDetail(),
      configureLicenceDetails, TransferOrderResource).then((res: ConfigureLicenceResponse) => {
        if (res.button) {
          this.transferOrderData.destinationSalesOrder.product.probes[index].configMappingRequest = res.probeObject;
        }
      }).finally(() => { this.loading = false; });
  }
  /**
  * Prepares and returns a transfer order validation request by collecting
  * product probe and bridge details from the destination sales order.
  * 
  * <AUTHOR>
  */

  private prepareTransferOrderRequest(): SalesOrderTransferValidateRequest {
    let sourceSalesOrder = this.transferOrderData.sourceSalesOrder;
    let destinationSalesOrder = this.transferOrderData.destinationSalesOrder;
    let bridges: SalesOrderTransferProductDetailValidateRequest[] = [];
    let probes: SalesOrderTransferProbeDetailValidateRequest[] = [];
    if (this.transferOrderData?.destinationSalesOrder?.product?.probes !== null) {
      this.transferOrderData?.destinationSalesOrder?.product?.probes.forEach(probe => {
        probes.push(new SalesOrderTransferProbeDetailValidateRequest(probe.sourceSopmId, probe.sopmId, probe.entityPk, probe.configMappingRequest));
      });
    }

    if (this.transferOrderData?.destinationSalesOrder?.product?.bridges !== null) {
      this.transferOrderData?.destinationSalesOrder?.product?.bridges.forEach(bridge => {
        bridges.push(new SalesOrderTransferProductDetailValidateRequest(bridge.sourceSopmId, bridge.sopmId));
      });
    }

    let product = new SalesOrderTransferProductValidateRequest(bridges, probes);
    return new SalesOrderTransferValidateRequest(sourceSalesOrder?.id, destinationSalesOrder?.id, product, this.selectedProbeAndDevice?.sourceDeviceSelected, this.selectedProbeAndDevice?.sourceProbeSelected);
  }

  public detailPageToggle(productEntityId: number, productEntityValue: number, isTransferOrderReviewDisplay: boolean, isProbeDetailPageDiplay: boolean, isDeviceDetailPageDiplay: boolean): void {
    this.probeEntityId = productEntityId;
    this.bridgeEntityValue = productEntityValue;
    this.transferOrderReviewDisplay = isTransferOrderReviewDisplay;
    this.probeDetailPageDiplay = isProbeDetailPageDiplay;
    this.deviceDetailPageDiplay = isDeviceDetailPageDiplay;
  }

  public showTransferOrderReviewPage(): void {
    this.detailPageToggle(null, null, true, false, false);
    this.ngOnInit();
  }

  public showDeviceDetailPage(productEntityId: number): void {
    this.detailPageToggle(null, productEntityId, false, false, true);
  }

  public showProbeDetailPage(productEntityId: number): void {
    this.detailPageToggle(productEntityId, null, false, true, false);
  }
}
