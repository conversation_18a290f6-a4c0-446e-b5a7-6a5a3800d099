import { HttpTestingController } from '@angular/common/http/testing';
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService } from 'ngx-webstorage';
import { of } from 'rxjs';
import { ProbDetailResource } from '../app.constants';
import { ConfirmDialogService } from '../confirmationdialog/confirmation.service';
import { DeviceSearchRequest } from '../model/device/deviceSearchRequest.model';
import { BASE_URL, commonsProviders } from '../Tesing-Helper/test-utils';
import { DeviceService } from './device.service';
import { CommonsService } from './util/commons.service';
import { DownloadService } from './util/download.service';

describe('DeviceService', () => {
  let service: DeviceService;
  let httpMock: HttpTestingController;
  const baseUrl = BASE_URL;

  const mockConfirmService = {
    confirm: jasmine.createSpy().and.returnValue(Promise.resolve(true))
  };

  const mockDownloadService = {
    subscribedownloadZipFileForProbDetailPageSubject: jasmine.createSpy(),
    subscribeDownloadZipFileForProbSubject: jasmine.createSpy()
  };

  const mockCommonsService = {
    handleError: jasmine.createSpy().and.returnValue(() => of({ error: 'error' }))
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        DeviceService,
        { provide: ConfirmDialogService, useValue: mockConfirmService },
        { provide: DownloadService, useValue: mockDownloadService },
        { provide: CommonsService, useValue: mockCommonsService },
        { provide: ToastrService, useValue: {} },
        LocalStorageService,
        commonsProviders(null)
      ]
    });

    service = TestBed.inject(DeviceService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should fetch device list', () => {
    const mockRequest = { status: 'test' } as DeviceSearchRequest;
    const mockResponse = [{ id: 1 }] as any;

    service.getDeviceList(mockRequest).subscribe(res => {
      expect(res.body).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(`${baseUrl}api/deviceMasters/search`);
    expect(req.request.method).toBe('POST');
    req.flush(mockResponse);
  });

  it('should fetch package version', () => {
    service.getpackageVersion().subscribe();
    const req = httpMock.expectOne(`${baseUrl}api/deviceMasters/packageVersion`);
    expect(req.request.method).toBe('GET');
    req.flush([]);
  });

  it('should trigger prob download for ProbDetailResource', fakeAsync(() => {
    service.dowloadSasUriofFeatureLicenseConfirmationModel(ProbDetailResource);
    tick();
    expect(mockDownloadService.subscribedownloadZipFileForProbDetailPageSubject).toHaveBeenCalled();
  }));

  it('should trigger prob download for other resource', fakeAsync(() => {
    service.dowloadSasUriofFeatureLicenseConfirmationModel('OTHER_RESOURCE');
    tick();
    expect(mockDownloadService.subscribeDownloadZipFileForProbSubject).toHaveBeenCalled();
  }));

  it('should update device type to client', () => {
    service.updateDeviceTypeToClient(123).subscribe();
    const req = httpMock.expectOne(`${baseUrl}api/deviceMasters/type/client/123`);
    expect(req.request.method).toBe('PUT');
    req.flush({});
  });

  it('should update device type to demo', () => {
    service.updateDeviceTypeToDemo(123).subscribe();
    const req = httpMock.expectOne(`${baseUrl}api/deviceMasters/type/demo/123`);
    expect(req.request.method).toBe('PUT');
    req.flush({});
  });

  it('should update device type to test', () => {
    service.updateDeviceTypeToTest(123).subscribe();
    const req = httpMock.expectOne(`${baseUrl}api/deviceMasters/type/test/123`);
    expect(req.request.method).toBe('PUT');
    req.flush({});
  });

  it('should update device state', () => {
    service.updateDeviceState(123, true).subscribe();
    const req = httpMock.expectOne(`${baseUrl}api/deviceMasters/state/123?locked=true`);
    expect(req.request.method).toBe('PUT');
    req.flush({});
  });

  it('should associate device with sales order', () => {
    const body = { email: '<EMAIL>' } as any;
    service.associationDeviceWithSalesOrder([1], body).subscribe();
    const req = httpMock.expectOne(`${baseUrl}api/deviceMasters/soDetails/1`);
    expect(req.request.method).toBe('PUT');
    req.flush({});
  });

  it('should get device detail', () => {
    service.getDeviceDetail(101).subscribe();
    const req = httpMock.expectOne(`${baseUrl}api/deviceMasters/101`);
    expect(req.request.method).toBe('GET');
    req.flush({});
  });

  it('should get release version details', () => {
    const request = { deviceId: 1 };
    service.getReleaseVersionDetail(request).subscribe();
    const req = httpMock.expectOne(`${baseUrl}api/deviceMasters/release`);
    expect(req.request.method).toBe('POST');
    req.flush({});
  });

  it('should assign selected release version', () => {
    const request = { deviceId: 10, releaseId: 200 };
    service.assignSelectedReleaseVersion(request).subscribe();
    const req = httpMock.expectOne(`${baseUrl}api/deviceMasters/release/10?releaseId=200`);
    expect(req.request.method).toBe('PUT');
    req.flush({});
  });

  it('should generate CSV file for device', () => {
    service.generateCSVFileForDevice({ type: 'TEST' }).subscribe();
    const req = httpMock.expectOne(`${baseUrl}api/deviceMasters/generateCSV`);
    expect(req.request.method).toBe('POST');
    req.flush({});
  });

  it('should download CSV file for device', () => {
    const mockBlob = new Blob(['column1,column2\nvalue1,value2'], { type: 'text/csv' });

    service.downloadCSVFileForDevice('testfile.csv').subscribe(response => {
      expect(response).toBeTruthy();
      expect(response instanceof Blob).toBeTrue();
    });

    const req = httpMock.expectOne(`${baseUrl}api/deviceMasters/downloadDevicesData/testfile.csv`);
    expect(req.request.method).toBe('GET');
    expect(req.request.responseType).toBe('blob');

    req.flush(mockBlob);
  });


  it('should disable product status for device', () => {
    service.disableProductStatusForDevice([1, 2]).subscribe();
    const req = httpMock.expectOne(`${baseUrl}api/deviceMasters/status/disable/1,2`);
    expect(req.request.method).toBe('PUT');
    req.flush({});
  });

  it('should mark product as RMA for device', () => {
    service.rmaProductStatusForDevice([1, 2]).subscribe();
    const req = httpMock.expectOne(`${baseUrl}api/deviceMasters/status/rma/1,2`);
    expect(req.request.method).toBe('PUT');
    req.flush({});
  });

  it('should toggle edit enable/disable for device', () => {
    service.editEnableDisableForDevice(123, true).subscribe();
    const req = httpMock.expectOne(`${baseUrl}api/deviceMasters/edit/123?enable=true`);
    expect(req.request.method).toBe('PUT');
    req.flush({});
  });
});
