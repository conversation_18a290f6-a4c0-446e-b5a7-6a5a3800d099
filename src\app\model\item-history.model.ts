
export interface ItemHistory {
    status?: string;
    description?: string;
    createdDate?: number;
    createdBy?: number;
    modifiedDate?: number;
    modifiedBy?: number;
}

export class ItemHistory implements ItemHistory {
    constructor(
        public status?: string,
        public description?: string,
        public createdDate?: number,
        public createdBy?: number,
        public modifiedDate?: number,
        public modifiedBy?: number
    ) { }
}
