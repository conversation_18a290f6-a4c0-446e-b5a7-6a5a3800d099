import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { UpdateAssociationComponent } from '../device/update-association/update-association.component';

@Injectable({
  providedIn: 'root'
})
export class UpdateAssociationService {

  constructor(
    private modalService: NgbModal
  ) { }

  public openUpdateAssociationModel(
    title: string,
    message: string,
    btnOkText: string,
    btnCancelText: string,
    dialogSize: 'sm' | 'lg' = 'sm'): Promise<boolean> {
    const modalRef = this.modalService.open(UpdateAssociationComponent, { size: dialogSize, windowClass: "modal fade modal-dimmed", backdropClass: "modal-dimmed" });
    modalRef.componentInstance.title = title;
    modalRef.componentInstance.message = message;
    modalRef.componentInstance.btnOkText = btnOkText;
    modalRef.componentInstance.btnCancelText = btnCancelText;

    return modalRef.result;
  }
}
