import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DatePipe } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { AddOrUpdateProbeConfigGroupComponent } from './add-or-update-probe-config-group.component';

describe('AddOrUpdateProbeConfigGroupComponent', () => {
  let component: AddOrUpdateProbeConfigGroupComponent;
  let fixture: ComponentFixture<AddOrUpdateProbeConfigGroupComponent>;

  beforeEach(async () => {
    const toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    const confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['open', 'close']);

    const localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);

    // Mock the return value of retrieve for the getPermissionObject function
    localStorageServiceMock.retrieve.and.returnValue('mockedPermissionObject');
    await TestBed.configureTestingModule({
      declarations: [AddOrUpdateProbeConfigGroupComponent],
      imports: [FormsModule, ReactiveFormsModule],
      providers: [
        SessionStorageService,
        DatePipe,
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(AddOrUpdateProbeConfigGroupComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
