import { DatePipe } from '@angular/common';
import { HttpResponse } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { commonsProviders } from '../Tesing-Helper/test-utils';
import { API_TIME_OUT } from '../app.constants';
import { ConfirmDialogService } from '../confirmationdialog/confirmation.service';
import { JobHistoryResponse } from '../model/job/jobHistoryResponse.model';
import { ExceptionHandlingService } from '../shared/ExceptionHandling.service';
import { SSOLoginService } from '../shared/Service/SSO/ssologin.service';
import { JobDetailComponent } from './job-detail.component';
import { JobService } from '../shared/Service/JobService/job.service';



describe('JobDetailComponent', () => {
  let component: JobDetailComponent;
  let fixture: ComponentFixture<JobDetailComponent>;
  let jobService: JobService;
  let exceptionHandlingService: ExceptionHandlingService;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let jobHistoryResponse = {
    "jobType": "FIRMWARE",
    "deviceId": "292020005181800046",
    "jobId": "97",
    "statusHistory": [{
      "status": "CREATED",
      "timestamp": 1725606890358
    }, {
      "status": "RECEIVED",
      "timestamp": 1725606892826
    }, {
      "status": "EXPIRED",
      "timestamp": 1729505700481
    }],
    "firmwareResponse": {
      "version": "9.0.0.0",
      "jsonVersion": null
    },
    "jsonVersion": null,
    "jobScheduleStatusId": 75,
    "null": false
  }
  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    jobService = jasmine.createSpyObj('JobService', ['getJobHistory']);
    exceptionHandlingService = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);

    await TestBed.configureTestingModule({
      declarations: [JobDetailComponent],
      imports: [],
      providers: [
        JobService,
        DatePipe,
        ExceptionHandlingService,
        LocalStorageService,
        SessionStorageService,
        ConfirmDialogService,
        ExceptionHandlingService,
        SSOLoginService,
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(JobDetailComponent);
    component = fixture.componentInstance;
    jobService = TestBed.inject(JobService);
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call getHistory Method', () => {
    spyOn(jobService, 'getJobHistory').and.returnValue(of(new HttpResponse<JobHistoryResponse>({
      body: jobHistoryResponse,
      status: 500, // Mock HTTP status indicating a server error
      statusText: 'OK',
    })));
    component.ngOnInit();
    fixture.detectChanges();
    component.refreshJobDetailPage();
    expect(component.jobHistoryResponse).toEqual(jobHistoryResponse);
    expect(component.loading).toBeFalse();

    const jobIdElement = fixture.debugElement.query(By.css('input[name="connectionState"]')).nativeElement;
    const jobTypeElement = fixture.debugElement.query(By.css('input[name="jobType"]')).nativeElement;
    const deviceIdeElement = fixture.debugElement.query(By.css('input[name="deviceId"]')).nativeElement;

    expect(jobIdElement.value).toBe(jobHistoryResponse.jobId);
    expect(jobTypeElement.value).toBe(jobHistoryResponse.jobType);
    expect(deviceIdeElement.value).toBe(jobHistoryResponse.deviceId);
  });
  it('should call toastrService.error with INTERNAL_SERVER_ERROR', () => {

    spyOn(jobService, 'getJobHistory').and.returnValue(
      throwError(() => ({
        status: 504, // HTTP status code indicating a server error
        statusText: 'Internal Server Error',
        message: 'Api Timeout'
      }))
    );


    // Spy on the customErrorMessage method in exceptionHandlingService to verify error handling
    spyOn(exceptionHandlingService, 'customErrorMessage').and.callThrough();

    // Act: Trigger the component's initialization to invoke getJobList and error handling
    component.ngOnInit();

    // Assert: Ensure customErrorMessage was called for error handling
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();

    // Assert: Confirm toastrService.error was called with an API_TIME_OUT message
    expect(toastrServiceMock.error).toHaveBeenCalledWith(API_TIME_OUT);
  });
});
