import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { DeviceListResource, ListKitManagementResource, OTSKitManagementListResource, } from 'src/app/app.constants';
import { ImportCsvFileComponent } from 'src/app/CommonPopupModel/import-csv-file/import-csv-file.component';
import { ImportFilePopupInput } from 'src/app/model/importCSVFile/ImportFilePopupInput.model';


@Injectable({
  providedIn: 'root'
})
export class ImportCsvfileService {

  constructor(private modalService: NgbModal) { }

  /**
   * <AUTHOR>
   * @param importFilePopupInput 
   * @returns Open CSV File import model
   */
  public openImportCSVModel(importFilePopupInput: ImportFilePopupInput): Promise<boolean> {
    const modalRef = this.modalService.open(ImportCsvFileComponent, { backdrop: "static" });
    modalRef.componentInstance.importFilePopupInput = importFilePopupInput;
    return modalRef.result;
  }

  /**
   * <AUTHOR>
   * @returns ImportFilePopupInput (Csv Model Input Parameter For Device)
   */
  public getImportFilePopupInputForDevice(): ImportFilePopupInput {
    return new ImportFilePopupInput('Update Device from CSV',
      "Choose CSV file for update devices", true,
      "deviceUpdate.csv", null, "Cancel", DeviceListResource);
  }

  /**
  * <AUTHOR>
  * @returns ImportFilePopupInput (Csv Model Input Parameter For Brige Kitmanagement)
  */
  public getImportFilePopupInputForKitManagement(): ImportFilePopupInput {
    return new ImportFilePopupInput('Update Bridge Kit Management from CSV',
      "Choose CSV file for update Bridge Kit Management", true,
      "D00001AB_bridgeKitList.csv", null, "Cancel", ListKitManagementResource);
  }
  /**
  * <AUTHOR>
  * @returns ImportFilePopupInput (Csv Model Input Parameter For OTS kitmanagement)
  */
  public getImportFilePopupInputForOTSKitManagement(): ImportFilePopupInput {
    return new ImportFilePopupInput('Update OTS World Kit Management from CSV',
      "Choose CSV file for update OTS World Kit Management.", true,
      "D00001AB_otsKitList.csv", null, "Cancel", OTSKitManagementListResource);
  }

}
