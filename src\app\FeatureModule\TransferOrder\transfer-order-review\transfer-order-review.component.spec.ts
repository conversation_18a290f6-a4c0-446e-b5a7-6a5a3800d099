import { HttpResponse } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { SalesOrderTransferProductRequest } from 'src/app/model/device/SalesOrderTransferProductRequest.model';
import { ConfigMappingRequest } from 'src/app/model/probe/ConfigMappingRequest.model';
import { ConfigureLicenceResponse } from 'src/app/model/probe/ConfigureLicenceResponse.model';
import { SalesOrderTransferProbeRequest } from 'src/app/model/probe/SalesOrderTransferProbeRequest.model';
import { SalesOrderTransferSuccessResponse } from 'src/app/model/SalesOrder/SalesOrderTransferSuccessResponse.model';
import { SalesOrderTransferValidateRequest } from 'src/app/model/SalesOrder/SalesOrderTransferValidateRequest.model';
import { SourceSelectedProbeAndDevice } from 'src/app/model/SalesOrder/SourceSelectedProbeAndDevice.model';
import { TransferOrderProductReviewResponse } from 'src/app/model/SalesOrder/TransferOrderProductReviewResponse.model';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { UpdateFeaturesService } from 'src/app/shared/modalservice/update-features.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { FeatureAndPresetInformationDisplayPipe } from 'src/app/shared/pipes/Common/FeatureAndPresetInformationDisplayPipe.pipe';
import { ConfigLicenceValidityDisplay } from 'src/app/shared/pipes/Probe/config-licence-validty-display.pipe';
import { SalesOrderStatusDisplay } from 'src/app/shared/pipes/SalesOrder/SalesOrderStatusDisplay.pipe';
import { SalesOrderApiCallService } from 'src/app/shared/Service/SalesOrderService/sales-order-api-call.service';
import { TransferOrderService } from 'src/app/shared/Service/TransferOrderService/transfer-order.service';
import { destinationSalesOrder, transferOrderResponse } from 'src/app/Tesing-Helper/SalesOrder/salesOrder';
import { commonsProviders, mockError } from 'src/app/Tesing-Helper/test-utils';
import { TransferOrderReviewComponent } from './transfer-order-review.component';

describe('TransferOrderReviewComponent', () => {
  let component: TransferOrderReviewComponent;
  let fixture: ComponentFixture<TransferOrderReviewComponent>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let salesOrderApiCallServiceSpy: jasmine.SpyObj<SalesOrderApiCallService>;
  let updateFeaturesServiceSpy: jasmine.SpyObj<UpdateFeaturesService>;
  let dialogServiceSpy: jasmine.SpyObj<ConfirmDialogService>;
  let transferOrderServiceSpy: jasmine.SpyObj<TransferOrderService>;
  let permissionServiceSpy: jasmine.SpyObj<PermissionService>;

  const transferOrderReviewResponse: TransferOrderProductReviewResponse = new TransferOrderProductReviewResponse(transferOrderResponse, destinationSalesOrder, false, [], false, []);
  const mockReviewOrderRequest = new SalesOrderTransferValidateRequest(1, 2, null, null, null);
  const mockSelectedProbeAndDevice = new SourceSelectedProbeAndDevice(null, null);

  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    salesOrderApiCallServiceSpy = jasmine.createSpyObj('SalesOrderApiCallService', ['TransferSalesOrder']);
    updateFeaturesServiceSpy = jasmine.createSpyObj('UpdateFeaturesService', ['openAssignProbeFeatureModel', 'getAssignProbeBasicModelConfigDetail']);
    dialogServiceSpy = jasmine.createSpyObj('ConfirmDialogService', ['confirm']);
    transferOrderServiceSpy = jasmine.createSpyObj('TransferOrderService', ['submitTranferOrderDetails']);
    permissionServiceSpy = jasmine.createSpyObj('PermissionService', ['getProbPermission', 'getDevicePermission']);

    await TestBed.configureTestingModule({
      declarations: [TransferOrderReviewComponent, ConfigLicenceValidityDisplay, FeatureAndPresetInformationDisplayPipe, SalesOrderStatusDisplay],
      imports: [
        ToastrModule.forRoot(),
      ],
      providers: [
        { provide: ConfirmDialogService, useValue: dialogServiceSpy },
        { provide: SalesOrderApiCallService, useValue: salesOrderApiCallServiceSpy },
        { provide: UpdateFeaturesService, useValue: updateFeaturesServiceSpy },
        { provide: TransferOrderService, useValue: transferOrderServiceSpy },
        { provide: PermissionService, useValue: permissionServiceSpy },
        AuthJwtService,
        LocalStorageService,
        ExceptionHandlingService,
        SessionStorageService,
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(TransferOrderReviewComponent);
    component = fixture.componentInstance;

    // Set up component inputs
    component.reviewOrderRequest = mockReviewOrderRequest;
    component.selectedProbeAndDevice = mockSelectedProbeAndDevice;
    component.isSalesOrderManual = false;

    // Set up default service mocks
    transferOrderServiceSpy.submitTranferOrderDetails.and.returnValue(Promise.resolve(transferOrderReviewResponse));
    permissionServiceSpy.getProbPermission.and.returnValue(true);
    permissionServiceSpy.getDevicePermission.and.returnValue(true);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize component on ngOnInit', async () => {
    fixture.detectChanges();
    await fixture.whenStable();

    expect(component.loading).toBeFalse();
    expect(component.transferOrderReviewDisplay).toBeTrue();
    expect(component.probeReadOnlyPermission).toBeTrue();
    expect(component.deviceReadOnlyPermission).toBeTrue();
    expect(transferOrderServiceSpy.submitTranferOrderDetails).toHaveBeenCalledWith(mockReviewOrderRequest);
    expect(component.transferOrderData).toEqual(transferOrderReviewResponse);
  });

  it('should handle transfer order service error on ngOnInit', async () => {
    transferOrderServiceSpy.submitTranferOrderDetails.and.returnValue(Promise.reject(mockError));

    fixture.detectChanges();
    await fixture.whenStable();

    expect(component.loading).toBeTruthy();
  });

  it('should successfully transfer order without warning', async () => {
    component.transferOrderData = transferOrderReviewResponse;
    salesOrderApiCallServiceSpy.TransferSalesOrder.and.returnValue(
      of(new HttpResponse<SalesOrderTransferSuccessResponse>({
        body: new SalesOrderTransferSuccessResponse("Order Transfer Successfully", null),
        status: 200,
        statusText: 'OK',
      }))
    );

    spyOn(component.backToDetailPage, 'emit');

    component.transferOrder(false);

    expect(salesOrderApiCallServiceSpy.TransferSalesOrder).toHaveBeenCalled();
    expect(toastrServiceMock.success).toHaveBeenCalledWith("Order Transfer Successfully");
    expect(component.backToDetailPage.emit).toHaveBeenCalled();
  });

  it('should handle transfer order error', async () => {
    component.transferOrderData = transferOrderReviewResponse;
    salesOrderApiCallServiceSpy.TransferSalesOrder.and.returnValue(throwError(() => mockError));

    component.transferOrder(false);

    expect(salesOrderApiCallServiceSpy.TransferSalesOrder).toHaveBeenCalled();
    expect(component.loading).toBeFalse();
  });

  it('should transfer order with warning confirmation', async () => {
    component.isSalesOrderManual = true;
    component.transferOrderData = new TransferOrderProductReviewResponse(transferOrderResponse, null, true, [], false, []);

    dialogServiceSpy.confirm.and.returnValue(Promise.resolve(true));
    salesOrderApiCallServiceSpy.TransferSalesOrder.and.returnValue(
      of(new HttpResponse<SalesOrderTransferSuccessResponse>({
        body: new SalesOrderTransferSuccessResponse(null, transferOrderReviewResponse),
        status: 200,
        statusText: 'OK',
      }))
    );

    component.transferOrder(true);
    await fixture.whenStable();

    expect(dialogServiceSpy.confirm).toHaveBeenCalled();
    expect(salesOrderApiCallServiceSpy.TransferSalesOrder).toHaveBeenCalled();
  });

  it('should not transfer order when warning confirmation is cancelled', async () => {
    component.transferOrderData = transferOrderReviewResponse;
    dialogServiceSpy.confirm.and.returnValue(Promise.resolve(false));

    component.transferOrder(true);
    await fixture.whenStable();

    expect(dialogServiceSpy.confirm).toHaveBeenCalled();
    expect(salesOrderApiCallServiceSpy.TransferSalesOrder).not.toHaveBeenCalled();
  });

  it('should handle transfer order with error messages', async () => {
    const errorResponse = new TransferOrderProductReviewResponse(transferOrderResponse, destinationSalesOrder, false, [], true, ['Error occurred']);
    component.transferOrderData = transferOrderReviewResponse;

    salesOrderApiCallServiceSpy.TransferSalesOrder.and.returnValue(
      of(new HttpResponse<SalesOrderTransferSuccessResponse>({
        body: new SalesOrderTransferSuccessResponse(null, errorResponse),
        status: 200,
        statusText: 'OK',
      }))
    );

    spyOn(component.backToDetailPage, 'emit');

    component.transferOrder(false);

    expect(toastrServiceMock.error).toHaveBeenCalledWith('Error occurred');
    expect(component.backToDetailPage.emit).toHaveBeenCalled();
  });

  it('should emit correct values for back() when isSalesOrderManual is true', () => {
    component.isSalesOrderManual = true;
    spyOn(component.hideAndShowTable, 'emit');

    component.back();

    expect(component.hideAndShowTable.emit).toHaveBeenCalledWith([true, false, false]);
  });

  it('should emit correct values for back() when isSalesOrderManual is false', () => {
    component.isSalesOrderManual = false;
    spyOn(component.hideAndShowTable, 'emit');

    component.back();

    expect(component.hideAndShowTable.emit).toHaveBeenCalledWith([false, true, false]);
  });

  it('should update probe features successfully', async () => {
    const mockConfigMappingRequest = new ConfigMappingRequest([], [], false);
    const mockConfigResponse = new ConfigureLicenceResponse(true, mockConfigMappingRequest);

    component.transferOrderData = transferOrderReviewResponse;
    updateFeaturesServiceSpy.getAssignProbeBasicModelConfigDetail.and.returnValue(null);
    updateFeaturesServiceSpy.openAssignProbeFeatureModel.and.returnValue(Promise.resolve(mockConfigResponse));

    component.updateProbeFeatures(mockConfigMappingRequest, 'probe', 0);
    await fixture.whenStable();

    expect(updateFeaturesServiceSpy.openAssignProbeFeatureModel).toHaveBeenCalled();
    expect(component.transferOrderData.destinationSalesOrder.product.probes[0].configMappingRequest).toEqual(mockConfigMappingRequest);
  });

  it('should not update probe features when user cancels', async () => {
    const mockConfigMappingRequest = new ConfigMappingRequest([], [], false);
    const mockConfigResponse = new ConfigureLicenceResponse(false, null);

    component.transferOrderData = transferOrderReviewResponse;
    updateFeaturesServiceSpy.getAssignProbeBasicModelConfigDetail.and.returnValue(null);
    updateFeaturesServiceSpy.openAssignProbeFeatureModel.and.returnValue(Promise.resolve(mockConfigResponse));

    const originalConfig = component.transferOrderData.destinationSalesOrder.product.probes[0].configMappingRequest;

    component.updateProbeFeatures(mockConfigMappingRequest, 'probe', 0);
    await fixture.whenStable();

    expect(updateFeaturesServiceSpy.openAssignProbeFeatureModel).toHaveBeenCalled();
    expect(component.transferOrderData.destinationSalesOrder.product.probes[0].configMappingRequest).toEqual(originalConfig);
  });

  it('should toggle detail pages correctly', () => {
    component.detailPageToggle(123, 456, false, true, false);

    expect(component.probeEntityId).toBe(123);
    expect(component.bridgeEntityValue).toBe(456);
    expect(component.transferOrderReviewDisplay).toBeFalse();
    expect(component.probeDetailPageDiplay).toBeTrue();
    expect(component.deviceDetailPageDiplay).toBeFalse();
  });

  it('should show transfer order review page', () => {
    spyOn(component, 'ngOnInit');

    component.showTransferOrderReviewPage();

    expect(component.probeEntityId).toBeNull();
    expect(component.bridgeEntityValue).toBeNull();
    expect(component.transferOrderReviewDisplay).toBeTrue();
    expect(component.probeDetailPageDiplay).toBeFalse();
    expect(component.deviceDetailPageDiplay).toBeFalse();
    expect(component.ngOnInit).toHaveBeenCalled();
  });

  it('should show device detail page', () => {
    component.showDeviceDetailPage(789);

    expect(component.probeEntityId).toBeNull();
    expect(component.bridgeEntityValue).toBe(789);
    expect(component.transferOrderReviewDisplay).toBeFalse();
    expect(component.probeDetailPageDiplay).toBeFalse();
    expect(component.deviceDetailPageDiplay).toBeTrue();
  });

  it('should show probe detail page', () => {
    component.showProbeDetailPage(321);

    expect(component.probeEntityId).toBe(321);
    expect(component.bridgeEntityValue).toBeNull();
    expect(component.transferOrderReviewDisplay).toBeFalse();
    expect(component.probeDetailPageDiplay).toBeTrue();
    expect(component.deviceDetailPageDiplay).toBeFalse();
  });

  it('should set serial number correctly when source device is selected', async () => {
    const mockDevice = new SalesOrderTransferProductRequest(1, 'DEVICE123');
    const mockSelectedProbeAndDevice = new SourceSelectedProbeAndDevice(mockDevice, null);
    component.selectedProbeAndDevice = mockSelectedProbeAndDevice;

    fixture.detectChanges();
    await fixture.whenStable();

    expect(component.serialNumber).toBe('- DEVICE123');
  });

  it('should set serial number correctly when source probe is selected', async () => {
    const mockProbe = new SalesOrderTransferProbeRequest(1, 'PROBE456', 'TestProbe');
    const mockSelectedProbeAndDevice = new SourceSelectedProbeAndDevice(null, mockProbe);
    component.selectedProbeAndDevice = mockSelectedProbeAndDevice;

    fixture.detectChanges();
    await fixture.whenStable();

    expect(component.serialNumber).toBe('- PROBE456');
  });

  it('should set empty serial number when no device or probe is selected', async () => {
    const mockSelectedProbeAndDevice = new SourceSelectedProbeAndDevice(null, null);
    component.selectedProbeAndDevice = mockSelectedProbeAndDevice;

    fixture.detectChanges();
    await fixture.whenStable();

    expect(component.serialNumber).toBe('');
  });

  it('should set bothSalesOrderManual correctly when both orders are manual', async () => {
    const manualOrderResponse = new TransferOrderProductReviewResponse(transferOrderResponse, destinationSalesOrder, false, [], false, []);
    manualOrderResponse.sourceSalesOrderIsManual = true;
    manualOrderResponse.destinationSalesOrderIsManual = true;

    transferOrderServiceSpy.submitTranferOrderDetails.and.returnValue(Promise.resolve(manualOrderResponse));

    fixture.detectChanges();
    await fixture.whenStable();

    expect(component.bothSalesOrderManual).toBeTrue();
  });

});
