import { Component, Input } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-inventory-operations',
  templateUrl: './inventory-operations.component.html',
  styleUrls: ['./inventory-operations.component.css']
})
export class InventoryOperationsComponent {

  @Input('btnOkText') btnOkText;
  @Input('btnCancelText') btnCancelText;
  @Input('header') header;
  @Input('mainbody') mainbody;
  @Input('activeModelReference') activeModelReference;

  constructor(
    private activeModal: NgbActiveModal
  ) { }

  public decline(): void {
    this.activeModal.close(false);
  }

  public accept(): void {
    this.activeModal.close(true);
  }

  public close(): void {
    this.activeModal.close(false);
  }

}
