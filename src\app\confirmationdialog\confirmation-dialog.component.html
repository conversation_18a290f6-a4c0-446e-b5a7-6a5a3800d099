<!-- <PERSON><PERSON> Container -->
<div aria-modal="true" aria-hidden="false">
  <!-- <PERSON><PERSON>er -->
  <div class="modal-header">
    <label class="modal-title">{{ title }}</label>
  </div>

  <!-- Modal Body -->
  <div class="modal-body">
    {{ message }}
  </div>

  <div *ngIf="infoMessage != null" class="mt-3">
    <strong *ngIf="infoMessagePrefix !=null">{{infoMessagePrefix}}</strong>{{infoMessage}}
  </div>

  <!-- Modal Footer -->
  <div class="modal-footer">
    <ng-template [ngIf]="btnCancelText != null">
      <button type="button" class="btn btn-sm btn-outline-secondary" id="confirmationDeclineButton" (click)="decline()">
        {{ btnCancelText }}
      </button>
    </ng-template>
    <ng-template [ngIf]="btnOkText != null">
      <button type="button" class="btn btn-sm btn-orange" id="confirmationOkButton" (click)="accept()">
        {{ btnOkText }}
      </button>
    </ng-template>
  </div>
</div>