import { DatePipe } from '@angular/common';
import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { forkJoin, Subscription } from 'rxjs';
import { Job } from '../../app/model/job.model';
import { DeviceActivityListResource, ITEMS_PER_PAGE, SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, SMALL_TEXTBOX_MAX_LENGTH, SPECIAL_CHARACTER_PATTERN, SPECIAL_CHARACTER_ERROR_MESSAGE } from '../app.constants';
import { MultiSelectDropdownSettings } from '../model/MultiSelectDropdownSettings.model';
import { EnumMapping } from '../model/common/EnumMapping.model';
import { IJobchange } from '../model/jobchange.model';
import { ExceptionHandlingService } from '../shared/ExceptionHandling.service';
import { JobService } from '../shared/Service/JobService/job.service';
import { AuthJwtService } from '../shared/auth-jwt.service';
import { DeviceService } from '../shared/device.service';
import { DeviceConnectionState } from '../shared/enum/Device/DeviceConnectionState.model';
import { PermissionAction } from '../shared/enum/Permission/permissionAction.enum';
import { collapseFilterTextEnum } from '../shared/enum/collapseFilterButtonText.enum';
import { PermissionService } from '../shared/permission.service';
import { CommonsService } from '../shared/util/commons.service';
import { KeyValueMappingServiceService } from '../shared/util/key-value-mapping-service.service';
import { MultiSelectDropDownSettingService } from '../shared/util/multi-select-drop-down-setting.service';


@Component({
  selector: 'app-device-acivities',
  templateUrl: './device-acivities.component.html',
  styleUrls: ['./device-acivities.component.css']
})
export class DeviceAcivitiesComponent implements OnInit {
  currentAccount: any;
  jobs: Job[];
  jobSchedules: Job[];
  error: any;
  success: any;
  eventSubscriber: Subscription;
  routeData: any;
  links: any;
  totalItems: any;
  itemsPerPage: any;
  page: number = 0;
  previousPage: any;
  reverse: any;
  dataSource: Job[];
  created: any;
  modified: any;
  starter: any;
  ender: any;
  display = 'none';
  modelId: any;
  job: Job;
  sts: any;
  loading = false;
  deviceActivityListResource = DeviceActivityListResource;

  // filter
  deviceConnectionState: Array<EnumMapping> = [];
  pkg_v: any[];
  jobTypes: string[];
  jobStatusList: string[];
  // multiselect
  disabled = false;
  ShowFilter = false;
  limitSelection = false;
  cities: Array<any> = [];
  selectedItems: Array<any> = [];
  systemSoftwearVersionDropdownSetting: MultiSelectDropdownSettings = null;
  deviceConnectionStateDropdownSetting: MultiSelectDropdownSettings = null;
  jobStatusDropdownSetting: MultiSelectDropdownSettings = null;
  JobTypeDropdownSetting: MultiSelectDropdownSettings = null;
  drpselectsize: number = ITEMS_PER_PAGE;

  totalJobDisplay: number = 0;
  totalJob: number = 0;

  //Text box max limit set
  textBoxMaxCharactersAllowedMessage: string = SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  specialCharacterErrorMessage: string = SPECIAL_CHARACTER_ERROR_MESSAGE;

  filterForm = this.fb.group({
    packageVersions: [],
    connectionState: [],
    drpJobTypes: [],
    jobStatus: [],
    jobDeviceId: new FormControl('', [Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH), Validators.pattern(SPECIAL_CHARACTER_PATTERN)])
  });

  deviceIdInput: number;
  displayJob: boolean = true;
  displayDeviceDetail: boolean = false;
  displayJobDetail: boolean = false;

  //input Job
  jobScheduleStatusId: number;
  isFilterHidden: boolean = false;
  hideShowFilterButtonText: string = collapseFilterTextEnum.HIDE_FILTER;

  // show entry selection
  dataSizes: string[] = [];

  //Permission
  deviceReaderPermission: boolean = false;
  jobReaderPermission: boolean = false;



  constructor(
    protected jobService: JobService,
    protected deviceService: DeviceService,
    protected router: Router,
    private datePipe: DatePipe,
    private fb: FormBuilder,
    private commonsService: CommonsService,
    private permissionService: PermissionService,
    private toste: ToastrService,
    private exceptionService: ExceptionHandlingService,
    private authservice: AuthJwtService,
    private multiSelectDropDownSettingService: MultiSelectDropDownSettingService,
    private keyValueMappingServiceService: KeyValueMappingServiceService
  ) {
    this.itemsPerPage = ITEMS_PER_PAGE;
  }


  /**
   * Set job and Device permission
   */
  private setPermission(): void {
    this.deviceReaderPermission = this.permissionService.getDevicePermission(PermissionAction.GET_DEVICE_ACTION);
    this.jobReaderPermission = this.permissionService.getJobPermission(PermissionAction.GET_JOB_ACTION);
  }

  openJobDetail(job) {
    this.jobScheduleStatusId = job.jobScheduleStatusId;
    this.displayJob = false;
    this.displayDeviceDetail = false;
    this.displayJobDetail = true;
  }
  /**
   * Clear All the filter
   * 
   * <AUTHOR>
   */
  private clearAllFilter(): void {
    let packageVersions = this.filterForm.get('packageVersions');
    packageVersions.setValue(null);
    let connectionState = this.filterForm.get('connectionState');
    connectionState.setValue(null);
    let jobDeviceId = this.filterForm.get('jobDeviceId');
    jobDeviceId.setValue(null);
    let drpJobTypes = this.filterForm.get('drpJobTypes');
    drpJobTypes.setValue(null);
    let jobStatus = this.filterForm.get('jobStatus');
    jobStatus.setValue(null);
    this.page = 0;
  }

  /**
   * Clear all the filter and reload data
   * 
   * <AUTHOR>
   */
  public clearFilter(): void {
    this.clearAllFilter()
    this.loadAll();
  }
  checkArray(value) {
    if (value != null && value.length > 0) {
      return false;
    }
    return true;

  }
  searchJobFiltet() {
    let packageVersions = this.checkArray(this.filterForm.get('packageVersions').value) ? null : this.filterForm.get('packageVersions').value;
    let connectionState = this.checkArray(this.filterForm.get('connectionState').value) ? null : this.filterForm.get('connectionState').value;
    let drpJobTypes = this.checkArray(this.filterForm.get('drpJobTypes').value) ? null : this.filterForm.get('drpJobTypes').value;
    let jobDeviceId = this.filterForm.get('jobDeviceId').value == "" ? null : this.filterForm.get('jobDeviceId').value;
    let jobStatus = this.checkArray(this.filterForm.get('jobStatus').value) ? null : this.filterForm.get('jobStatus').value;
    if (packageVersions == null && connectionState == null && jobDeviceId == null && drpJobTypes == null && jobStatus == null) {
      this.toste.info("Please Select Filter To Search")
    }
    else {
      this.page = 0;
      this.loadAll();
    }
  }

  jobTypeValue(value: string[]): string {
    if (value != null && value.length == 1) {
      return value[0];
    }
    return null;
  }
  public loadAll(): void {
    this.loading = true;
    if (this.filterForm.invalid) {
      this.filterForm.reset();
    }
    this.filterForm.get('jobDeviceId').setValue(this.commonsService.checkNullFieldValue(this.filterForm.get('jobDeviceId').value));
    let connectionState = this.commonsService.getSelectedValueFromEnum(this.filterForm.get('connectionState').value);
    this.jobService
      .getJobList(
        {
          packageVersions: this.filterForm.get('packageVersions').value,
          connectionState: connectionState,
          jobDeviceId: this.filterForm.get('jobDeviceId').value,
          drpJobTypes: this.jobTypeValue(this.filterForm.get('drpJobTypes').value),
          jobStatus: this.filterForm.get('jobStatus').value,
        },
        {
          page: this.page - 1,
          size: this.itemsPerPage,
        }
      )
      ?.subscribe({
        next: (res: HttpResponse<IJobchange>) => {
          if (res.status != 200 || res.body == null) {
            this.jobs = [];
            this.loading = false;
            this.totalJobDisplay = 0;
            this.totalJob = 0;
          } else {
            this.paginateJobs(res?.body);
          }
        },
        error: (error: HttpErrorResponse) => {
          this.exceptionService.customErrorMessage(error);
          this.loading = false;
        }
      });
  }

  loadPage(page: number) {
    if (page !== this.previousPage) {
      this.previousPage = page;
      this.transition();
    }
  }

  transition() {
    this.router.navigate(['/modules']);
    this.loadAll();
  }

  ngOnInit() {
    if (!this.authservice.isAuthenticate()) {
      this.authservice.loginNavigate();
    } else {
      this.dataSizes = this.commonsService.accessDataSizes();
      this.displayJob = true;
      this.displayDeviceDetail = false;
      this.displayJobDetail = false;
      this.setPermission();
      this.itemsPerPage = ITEMS_PER_PAGE;
      this.drpselectsize = ITEMS_PER_PAGE;
      this.page = 0;
      this.previousPage = 1;
      this.getInitCall();
      this.clearFilter();
    }

  }

  changeDataSize(datasize): void {
    this.loading = true;
    this.itemsPerPage = datasize.target.value;
    this.loadAll();
  }

  public async getInitCall(): Promise<void> {
    this.systemSoftwearVersionDropdownSetting = this.multiSelectDropDownSettingService.getSystemSoftwearVersionDropdownSetting();
    this.deviceConnectionStateDropdownSetting = this.multiSelectDropDownSettingService.getDeviceConnectionStateDropdownSetting();
    this.jobStatusDropdownSetting = this.multiSelectDropDownSettingService.getJobStatusDropdownSetting();
    this.JobTypeDropdownSetting = this.multiSelectDropDownSettingService.getJobTypeDropdownSetting();
    this.deviceConnectionState = this.keyValueMappingServiceService.enumOptionToList(DeviceConnectionState);
    // for filter
    this.initApiCall();
  }

  /**
  * Initialize API calls for fetching package versions, job types, and job statuses.
  *
  * <AUTHOR>
  */
  public initApiCall(): void {
    forkJoin({
      pkg_v: this.deviceService.getpackageVersion(),
      jobTypes: this.jobService.getJobType(),
      jobStatusList: this.jobService.getJobStatus(),
    }).subscribe({
      next: ({ pkg_v, jobTypes, jobStatusList }) => {
        this.pkg_v = this.commonsService.checkForNull(pkg_v.body);
        this.jobTypes = this.commonsService.checkForNull(jobTypes.body);
        this.jobStatusList = this.commonsService.checkForNull(jobStatusList.body);
      },
      error: (error) => {
        this.exceptionService.customErrorMessage(error);
      }
    });
  }

  /**
  * Refresh Button Click
  * 
  * <AUTHOR>
  */
  public clickOnRefreshButton(): void {
    this.initApiCall();
    this.loadAll();
  }


  trackId(index: number, item: Job) {
    return item.id;
  }

  protected paginateJobs(data: any) {
    this.totalItems = parseInt(data.totalElements, 10);
    this.jobs = data.content;
    this.page = data.number + 1;
    this.totalJob = data.totalElements;
    this.totalJobDisplay = data.numberOfElements;

    for (let index = 0; index < data.content?.length; index++) {
      this.created = this.datePipe.transform(new Date(data.content[index].createdDate), 'MMM d, y, h:mm:ss a');
      this.jobs[index].createdDate = this.created;
      this.modified = this.datePipe.transform(new Date(data.content[index].modifiedDate), 'MMM d, y, h:mm:ss a');
      this.jobs[index].modifiedDate = this.modified;
      this.starter = this.datePipe.transform(new Date(data.content[index].startDate), 'MMM d, y, h:mm:ss a');
      this.jobs[index].startDate = this.starter;
      this.ender = this.datePipe.transform(new Date(data.content[index].endDate), 'MMM d, y, h:mm:ss a');
      this.jobs[index].endDate = this.ender;
    }
    this.loading = false;
  }

  public deviceDetailModel(deviceMasterIdPk: number): void {
    this.deviceIdInput = deviceMasterIdPk;
    this.displayJob = false;
    this.displayDeviceDetail = true;
    this.displayJobDetail = false;
  }

  /**
   * Show job 
   * <AUTHOR>
   */
  public showJob(): void {
    this.displayJob = true;
    this.displayDeviceDetail = false;
    this.displayJobDetail = false;
    this.loadAll();
  }

  /**
  * Toggle Filter 
  *  
  */
  public toggleFilter(): void {
    this.isFilterHidden = !this.isFilterHidden;
    if (this.isFilterHidden) {
      this.hideShowFilterButtonText = collapseFilterTextEnum.SHOW_FILTER;
    } else {
      this.hideShowFilterButtonText = collapseFilterTextEnum.HIDE_FILTER;
    }
  }
}
