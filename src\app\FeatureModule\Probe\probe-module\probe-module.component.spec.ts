import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ProbeModuleComponent } from './probe-module.component';
import { ProbeOperationService } from '../ProbeService/Probe-Operation/probe-operation.service';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { ProbeListFilterRequestBody } from 'src/app/model/probe/ProbeListFilterRequestBody.model';
import { ProbListResource } from 'src/app/app.constants';

describe('ProbeModuleComponent', () => {
  let component: ProbeModuleComponent;
  let fixture: ComponentFixture<ProbeModuleComponent>;
  let probeOperationServiceSpy: jasmine.SpyObj<ProbeOperationService>;

  beforeEach(async () => {
    const spy = jasmine.createSpyObj('ProbeOperationService', ['callRefreshPageSubject']);

    await TestBed.configureTestingModule({
      declarations: [ProbeModuleComponent],
      providers: [
        { provide: ProbeOperationService, useValue: spy }
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(ProbeModuleComponent);
    component = fixture.componentInstance;
    probeOperationServiceSpy = TestBed.inject(ProbeOperationService) as jasmine.SpyObj<ProbeOperationService>;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.probeListFilterRequestBody).toBeNull();
    expect(component.isFilterHidden).toBe(false);
    expect(component.isProbeListingPageDisplay).toBe(true);
    expect(component.isProbeDetailPageDisplay).toBe(false);
    expect(component.loading).toBe(false);
    expect(component.probeListResource).toBe(ProbListResource);
  });

  it('should get probe id', () => {
    const probeId = 123;
    component.getProbeId(probeId);
    expect(component.probeIdInput).toBe(probeId);
  });

  it('should call filter page subject for reload page', () => {
    const isDefaultPageNumber = true;
    const isClearFilter = false;
    const expectedParameter = new ListingPageReloadSubjectParameter(true, isDefaultPageNumber, isClearFilter, false);

    component.filterPageSubjectCallForReloadPage(isDefaultPageNumber, isClearFilter);

    expect(probeOperationServiceSpy.callRefreshPageSubject).toHaveBeenCalledWith(
      expectedParameter,
      ProbListResource,
      component.isFilterHidden,
      component.probeListFilterRequestBody
    );
  });

  it('should update probe list filter request body', () => {
    const mockFilterBody = new ProbeListFilterRequestBody(null, null, null, null, null, null, null, null, null, null, null, null, null, null);
    component.updateProbeListFilterRequestBody(mockFilterBody);
    expect(component.probeListFilterRequestBody).toBe(mockFilterBody);
  });

  it('should update is filter hidden', () => {
    component.updateIsFilterHidden(true);
    expect(component.isFilterHidden).toBe(true);
  });

  it('should show probe detail', () => {
    component.showProbeDetail();
    expect(component.isProbeDetailPageDisplay).toBe(true);
    expect(component.isProbeListingPageDisplay).toBe(false);
  });

  it('should show probe listing from detail page', () => {
    component.isProbeDetailPageDisplay = true;
    component.isProbeListingPageDisplay = false;

    component.showProbe();

    expect(component.isProbeListingPageDisplay).toBe(true);
    expect(component.isProbeDetailPageDisplay).toBe(false);
    expect(probeOperationServiceSpy.callRefreshPageSubject).toHaveBeenCalledWith(
      jasmine.any(ListingPageReloadSubjectParameter),
      ProbListResource,
      component.isFilterHidden,
      component.probeListFilterRequestBody
    );
  });
});
