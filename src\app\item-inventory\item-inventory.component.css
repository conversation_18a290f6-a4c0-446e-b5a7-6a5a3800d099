@import '../../../src/assets/css/custom_style.css';
#model_table{
  display: table;
}

.active-pink-2 input[type=text]:focus:not([readonly]) {
    border-bottom: 1px solid #f48fb1;
    box-shadow: 0 1px 0 0 #f48fb1;
    }
    
    .active-pink input[type=text] {
    border-bottom: 1px solid #f48fb1;
    box-shadow: 0 1px 0 0 #f48fb1;
    }
    
    .active-purple-2 input[type=text]:focus:not([readonly]) {
    border-bottom: 1px solid #ce93d8;
    box-shadow: 0 1px 0 0 #ce93d8;
    }
    
    .active-purple input[type=text] {
    border-bottom: 1px solid #ce93d8;
    box-shadow: 0 1px 0 0 #ce93d8;
    }
    
    .active-cyan-2 input[type=text]:focus:not([readonly]) {
    border-bottom: 1px solid #4dd0e1;
    box-shadow: 0 1px 0 0 #4dd0e1;
    }
    
    .active-cyan input[type=text] {
    border-bottom: 1px solid #4dd0e1;
    box-shadow: 0 1px 0 0 #4dd0e1;
    }
    
    .active-cyan .fa,
    .active-cyan-2 .fa {
    color: #4dd0e1;
    }
    
    .active-purple .fa,
    .active-purple-2 .fa {
    color: #ce93d8;
    }
    
    .active-pink .fa,
    .active-pink-2 .fa {
    color: #f48fb1;
    }

    .inventory_table tr {
        line-height: 30px !important;
        min-height: 30px !important;
        height: 30px !important;
      }

    .inventory_table td { 
        text-overflow: ellipsis;
        overflow: hidden; 
        max-width: 150px; 
        white-space: nowrap;
      }
      .deviceSearch{
        margin-left: 10px;
        border: 1px solid #ccc;
        border-radius: 4px;
        height: auto;
        padding: 4px;
      }
      .video-filter {
        color: #f46c26;
      }
      
.hideLockedDiv {
  display: none;
}

.downloadButtonPosition {
  display: flex;
  justify-content: space-between;
}

#itemInventoryList .itemInventoryTable th{
    white-space: nowrap;
}

#itemInventoryList .itemInventoryTable .tableDataNoWrap{
  white-space: nowrap;
}

#itemInventoryList .itemInventoryTable .min-col-width{
   min-width: 270px;
}