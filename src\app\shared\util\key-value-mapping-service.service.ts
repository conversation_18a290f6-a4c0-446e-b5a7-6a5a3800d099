import { Injectable } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { CLIENT_DEVICE, DEMO_DEVICE, Edit_Disable, Edit_Enable, Locked, TEST_DEVICE, Unlocked } from 'src/app/app.constants';
import { BooleanKeyValueMapping } from 'src/app/model/common/BooleanKeyValueMapping.model';
import { EnumMapping } from 'src/app/model/common/EnumMapping.model';
import { deviceTypesEnum } from '../enum/deviceTypesEnum.enum';
import { inventoryStatusEnum } from '../enum/inventoryStatusEnum';
import { PrintListPipe } from '../pipes/printList.pipe';
import { CommonsService } from './commons.service';

@Injectable({
  providedIn: 'root'
})
export class KeyValueMappingServiceService {

  constructor(private printListPipe: PrintListPipe,
    private commonsService: CommonsService) { }

  /**
   * Feature Validity Period List
   * 
   * <AUTHOR>
   * @returns 
   */
  public featureValidityPeriodList(): BooleanKeyValueMapping[] {
    let validityList: BooleanKeyValueMapping[] = [];
    validityList.push(new BooleanKeyValueMapping("With expiry date", true));
    validityList.push(new BooleanKeyValueMapping("Without expiry date", false));
    return validityList;
  }


  /**
    * Get Locked/Unlocked Option List
    * <AUTHOR>
    * @returns 
    */
  public lockedUnlockOptionList(): Array<BooleanKeyValueMapping> {
    let lockedUnlockList: BooleanKeyValueMapping[] = [];
    lockedUnlockList.push(new BooleanKeyValueMapping(Locked, true));
    lockedUnlockList.push(new BooleanKeyValueMapping(Unlocked, false));
    return lockedUnlockList;
  }

  /**
  * Get Edit Enable/Disable Option List
  * <AUTHOR>
  * @returns 
  */
  public editEnableDisableOptionList(): Array<BooleanKeyValueMapping> {
    let enableDisableList: BooleanKeyValueMapping[] = [];
    enableDisableList.push(new BooleanKeyValueMapping(Edit_Enable, true));
    enableDisableList.push(new BooleanKeyValueMapping(Edit_Disable, false));
    return enableDisableList;
  }

  /**
   * Get Active/InActive Option List
   * 
   * <AUTHOR>
   * @returns 
   */
  public activeInActiveOptionList(): Array<BooleanKeyValueMapping> {
    let activeInActiveList: BooleanKeyValueMapping[] = [];
    activeInActiveList.push(new BooleanKeyValueMapping(inventoryStatusEnum.ACTIVE, true));
    activeInActiveList.push(new BooleanKeyValueMapping(inventoryStatusEnum.INACTIVE, false));
    return activeInActiveList;
  }

  public firmwareVideoOptionList(): Array<BooleanKeyValueMapping> {
    let firmwareVideoOptionList: BooleanKeyValueMapping[] = [];
    firmwareVideoOptionList.push(new BooleanKeyValueMapping("Yes", true));
    firmwareVideoOptionList.push(new BooleanKeyValueMapping("No", false));
    return firmwareVideoOptionList;
  }


  public getBooleanValue(value: string, field: string, booleanKeyValueMap: Map<string, Array<BooleanKeyValueMapping>>): string {
    let booleanKeyValueMappingList: Array<BooleanKeyValueMapping> = [];
    booleanKeyValueMappingList = booleanKeyValueMap.get(field).filter(obj => obj.value.toString() == value);
    return booleanKeyValueMappingList.length > 0 ? booleanKeyValueMappingList.map(obj => obj.key).toString() : null;
  }

  /**
   * Get Device Type Enum Option List
   * 
   * <AUTHOR>
   * @returns 
   */
  public deviceTypeEnumOptionList(): Array<EnumMapping> {
    let lockedUnlockList: EnumMapping[] = [];
    lockedUnlockList.push(new EnumMapping(deviceTypesEnum.CLIENT_DEVICE, CLIENT_DEVICE));
    lockedUnlockList.push(new EnumMapping(deviceTypesEnum.DEMO_DEVICE, DEMO_DEVICE));
    lockedUnlockList.push(new EnumMapping(deviceTypesEnum.TEST_DEVICE, TEST_DEVICE));
    return lockedUnlockList;
  }

  /**
  * Enum To List 
  * 
  * <AUTHOR>
  * @returns  Array<EnumMapping>
  */
  public enumOptionToList(enumdata): Array<EnumMapping> {
    return this.commonsService.getEnumToList(enumdata);
  }

  public getEnumKeyValue(value: string, field: string, enumKeyValueMap: Map<string, Array<EnumMapping>>): string {
    let enumKeyKeyValueMappingList: Array<EnumMapping> = [];
    let valueList = isNullOrUndefined(value) ? [] : value.split(",");
    enumKeyKeyValueMappingList = enumKeyValueMap.get(field).filter(obj => valueList.includes(obj.key));
    return enumKeyKeyValueMappingList.length > 0 ? this.printListPipe.transform(enumKeyKeyValueMappingList.map(obj => obj.value)) : null;
  }
}
