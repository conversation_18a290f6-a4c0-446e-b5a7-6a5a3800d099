import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { commonsProviders, testErrorHandling } from 'src/app/Tesing-Helper/test-utils';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { VideoService } from 'src/app/shared/videoservice/video.service';
import { UploadJsonDialogComponent } from './upload-json-dialog.component';

describe('UploadJsonDialogComponent', () => {
  let component: UploadJsonDialogComponent;
  let fixture: ComponentFixture<UploadJsonDialogComponent>;
  let exceptionHandlingService: ExceptionHandlingService;
  let videoApiCallServicespy: jasmine.SpyObj<VideoService>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;

  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj<ToastrService>('ToastrService', ['success', 'error', 'warning', 'info']);
    // Correct SpyObj creation for VideoService
    videoApiCallServicespy = jasmine.createSpyObj<VideoService>('VideoService', [
      'updateJson',
      'createJson',
    ]);

    await TestBed.configureTestingModule({
      declarations: [UploadJsonDialogComponent],
      imports: [ReactiveFormsModule],
      providers: [
        NgbActiveModal,
        VideoService,
        CommonsService,
        ExceptionHandlingService,
        AuthJwtService,
        SessionStorageService,
        LocalStorageService,
        ConfirmDialogService,
        { provide: VideoService, useValue: videoApiCallServicespy },
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(UploadJsonDialogComponent);
    component = fixture.componentInstance;
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should handle decline call', async () => {
    spyOn(component, 'decline').and.callThrough(); // Ensure method is spied but also executed

    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    const declineButton = fixture.debugElement.query(By.css('#decineJsonButton'))?.nativeElement;
    expect(declineButton).toBeTruthy(); // Ensure button exists

    declineButton.click();
    fixture.detectChanges();
    await fixture.whenStable();

    expect(component.decline).toHaveBeenCalled(); // Verify the function was called
  });

  it('should handle error in updateJson when videoZipFileId is set', () => {
    component.jsonId = 1;
    testErrorHandling(videoApiCallServicespy.updateJson, () => component.accept(), exceptionHandlingService, toastrServiceMock, fixture);
  });

  it('should handle error in createJson when videoZipFileId is set', () => {
    component.jsonId = null;
    testErrorHandling(videoApiCallServicespy.createJson, () => component.accept(), exceptionHandlingService, toastrServiceMock, fixture);
  });

});
