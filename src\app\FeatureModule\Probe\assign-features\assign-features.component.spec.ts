import { DatePipe } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NgbActiveModal, NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { ProbeApiService } from 'src/app/shared/Service/ProbeService/probe-api.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { AssignFeaturesComponent } from './assign-features.component';

describe('AssignFeaturesComponent', () => {
  let component: AssignFeaturesComponent;
  let fixture: ComponentFixture<AssignFeaturesComponent>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;

  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);

    await TestBed.configureTestingModule({
      declarations: [AssignFeaturesComponent],
      imports: [NgbModule],
      providers: [NgbActiveModal,
        ExceptionHandlingService,
        AuthJwtService,
        ProbeApiService,
        SessionStorageService,
        DatePipe,
        LocalStorageService,
        ConfirmDialogService,
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(AssignFeaturesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
