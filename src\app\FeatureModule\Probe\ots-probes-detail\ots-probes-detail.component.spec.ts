import { CommonModule, DatePipe } from '@angular/common';
import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ChangeDetectorRef, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError, Subject } from 'rxjs';
import { FeatureHistoryDetailHeader, ProbDetailResource, PROBE_DELETE, TRANSFER_ORDER } from '../../../app.constants';
import { ConfirmDialogService } from '../../../confirmationdialog/confirmation.service';
import { AssignFeaturesComponent } from '../assign-features/assign-features.component';
import { BasicModelConfig } from '../../../model/common/BasicModelConfig.model';
import { ListingPageReloadSubjectParameter } from '../../../model/common/listingPageReloadSubjectParameter.model';
import { Pageable } from '../../../model/common/pageable.model';
import { Sort } from '../../../model/common/sort.model';
import { TransferProductDetails } from '../../../model/device/TransferProductDetails.model';
import { DeviceListByProbeIdPagableResponse } from '../../../model/probe/DeviceListByProbeIdPagableResponse.model';
import { LicensesRequest } from '../../../model/probe/multiProbe/LicensesRequest.model';
import { ProbeDetailWithConfig } from '../../../model/probe/ProbeDetailWithConfig.model';
import { ProbeDeviceList } from '../../../model/probe/probeDeviceList.model';
import { ProbeHistoryPagableResponse } from '../../../model/probe/ProbeHistoryPagableResponse.model';
import { ProbeHistoryResponse } from '../../../model/probe/ProbeHistoryResponse.model';
import { AuthJwtService } from '../../../shared/auth-jwt.service';
import { ProductStatusEnum } from '../../../shared/enum/Common/ProductStatus.enum';
import { ProbeOperationsEnum } from '../../../shared/enum/Operations/ProbeOperations.enum';
import { OSTypeEnum } from '../../../shared/enum/Probe/OSTypeEnum.enum';
import { ExceptionHandlingService } from '../../../shared/ExceptionHandling.service';
import { CustomerAssociationService } from '../../../shared/modalservice/customer-association.service';
import { FeatureHistoryDetailService } from '../../../shared/modalservice/feature-history-detail.service';
import { UpdateFeaturesService } from '../../../shared/modalservice/update-features.service';
import { PermissionService } from '../../../shared/permission.service';
import { CommonBooleanValueDisplayPipe } from '../../../shared/pipes/common-boolean-value-display.pipe';
import { BooleanKeyValueMappingDisplayNamePipe } from '../../../shared/pipes/Common/BooleanKeyValueMappingDisplayNamePipe.pipe';
import { EnumMappingDisplayNamePipe } from '../../../shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { PrintListPipe } from '../../../shared/pipes/printList.pipe';
import { AssignConfigCheckBoxPipe } from '../../../shared/pipes/Probe/assign-config-checkbox.pipe';
import { AssignConfigDisablePipe } from '../../../shared/pipes/Probe/assign-config-disable.pipe';
import { ConfigBaseResponseDisplayPipe } from '../../../shared/pipes/Probe/config-base-response-display.pipe';
import { FeatureValidityOptionHideShowPipe } from '../../../shared/pipes/Probe/feature-validity-option-hide-show.pipe';
import { FeaturesBaseResponseDisplayPipe } from '../../../shared/pipes/Probe/features-base-response-display.pipe';
import { FeaturesCustomEndDateDisplayPipe } from '../../../shared/pipes/Probe/features-customEndDateDisplay.pipe';
import { FeaturesExpireDateDisplayPipe } from '../../../shared/pipes/Probe/features-expire-datedisplay.pipe';
import { FeaturesRadioButtonPipe } from '../../../shared/pipes/Probe/features-radio-button.pipe';
import { FeaturesStartEndDateDisplay } from '../../../shared/pipes/Probe/features-start-end-dateDisplay.pipe';
import { FeaturesValidityPartNumberDisplayPipe } from '../../../shared/pipes/Probe/features-validity-partNumber-Display.pipe';
import { HidePermissionNamePipe } from '../../../shared/pipes/Role/hidePermissionName.pipe';
import { CountryCacheService } from '../../../shared/Service/CacheService/countrycache.service';
import { PresetApiService } from '../../../shared/Service/PresetService/preset-api.service';
import { ProbeApiService } from '../../../shared/Service/ProbeService/probe-api.service';
import { ProbeService } from '../../../shared/Service/ProbeService/probe.service';
import { RoleApiCallService } from '../../../shared/Service/RoleService/role-api-call.service';
import { SalesOrderApiCallService } from '../../../shared/Service/SalesOrderService/sales-order-api-call.service';
import { SSOLoginService } from '../../../shared/Service/SSO/ssologin.service';
import { UpdateAssociationService } from '../../../shared/update-association.service';
import { CommonOperationsService } from '../../../shared/util/common-operations.service';
import { CommonsService } from '../../../shared/util/commons.service';
import { DownloadService } from '../../../shared/util/download.service';
import { KeyValueMappingServiceService } from '../../../shared/util/key-value-mapping-service.service';
import { ModuleValidationServiceService } from '../../../shared/util/module-validation-service.service';
import { ValidationService } from '../../../shared/util/validation.service';
import { commonsProviders } from '../../../Tesing-Helper/test-utils';
import { OtsProbesDetailComponent } from './ots-probes-detail.component';
import { TransferOrderModuleComponent } from '../../TransferOrder/transfer-order-module/transfer-order-module.component';
import { ProbeOperationService } from '../ProbeService/Probe-Operation/probe-operation.service';

describe('OtsProbesDetailComponent', () => {
  let component: OtsProbesDetailComponent;
  let fixture: ComponentFixture<OtsProbesDetailComponent>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let probeApiServiceSpy: jasmine.SpyObj<ProbeApiService>;
  let probeOperationServiceSpy: jasmine.SpyObj<ProbeOperationService>;
  let updateFeaturesServiceSpy: jasmine.SpyObj<UpdateFeaturesService>;
  let featureHistoryDetailServiceSpy: jasmine.SpyObj<FeatureHistoryDetailService>;
  let downloadServiceSpy: jasmine.SpyObj<DownloadService>;
  let commonOperationsServiceSpy: jasmine.SpyObj<CommonOperationsService>;
  let commonsServiceSpy: jasmine.SpyObj<CommonsService>;
  let keyValueMappingServiceSpy: jasmine.SpyObj<KeyValueMappingServiceService>;
  let moduleValidationServiceSpy: jasmine.SpyObj<ModuleValidationServiceService>;
  let exceptionServiceSpy: jasmine.SpyObj<ExceptionHandlingService>;

  // Mock data
  const mockProbeDetailWithConfig = new ProbeDetailWithConfig(
    4211,
    "Torso1, USB",
    "ENGDM1.8",
    "PART1234567890",
    "1.0.23",
    1740378882671,
    1740378849244,
    false,
    "system",
    null,
    null,
    null,
    null,
    1740378883139,
    "Algeria",
    false,
    true,
    null,
    false,
    false,
    [new LicensesRequest(1, "Trio 2.0", 1740378849244, -1, false, true)],
    [new LicensesRequest(1, "Gyn", 1740378849244, -1, false, true)],
    ProductStatusEnum.RMA,
    "Transfer Order"
  );

  const mockProbeHistoryResponse = new ProbeHistoryResponse(
    15784,
    1740041739079,
    "akshay.dobariya",
    ["CW Doppler", "Auto EF"],
    ["Heart", "Ob"],
    false
  );

  const mockSort = new Sort(true, false, false);
  const mockPageable = new Pageable(mockSort, 0, 10, 0, true, false);

  const mockProbeHistoryPagableResponse = new ProbeHistoryPagableResponse(
    mockPageable, 5, false, 50, 10, true, mockSort, 10, 0, false, [mockProbeHistoryResponse]
  );

  const mockProbeDeviceList = new ProbeDeviceList(
    4950, "107CF56A-EE4F-4041-B089-FEE35D95E33D", "IOS", "17.5.1",
    "********", "0.0.11", "Asia/Kolkata", "iPad", "Apple", "1", "1", null
  );

  const mockDeviceListResponse = new DeviceListByProbeIdPagableResponse(
    mockPageable, 5, false, 50, 10, true, mockSort, 10, 0, false, [mockProbeDeviceList]
  );


  beforeEach(async () => {
    // Create service spies
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    probeApiServiceSpy = jasmine.createSpyObj('ProbeApiService', ['getProbeDetailInfo', 'getProbeHistory', 'getDeviceListByProbeId']);
    probeOperationServiceSpy = jasmine.createSpyObj('ProbeOperationService', [
      'getProbeDetailLoadingSubject', 'getProbeDetailRefreshSubject', 'getTransferProbeUISubject',
      'changeOperationForProbe', 'transferProbes'
    ]);
    updateFeaturesServiceSpy = jasmine.createSpyObj('UpdateFeaturesService', ['openAssignProbeFeatureModel']);
    featureHistoryDetailServiceSpy = jasmine.createSpyObj('FeatureHistoryDetailService', ['openFeatureHistoryDetailModel']);
    downloadServiceSpy = jasmine.createSpyObj('DownloadService', ['getisLoadingSubjectForProbDetailPage', 'getdownloadZipFileForProbDetailPageSubject']);
    commonOperationsServiceSpy = jasmine.createSpyObj('CommonOperationsService', ['getCommonLoadingSubject', 'accessProbeOperations']);
    commonsServiceSpy = jasmine.createSpyObj('CommonsService', ['accessDataSizes']);
    keyValueMappingServiceSpy = jasmine.createSpyObj('KeyValueMappingServiceService', [
      'enumOptionToList', 'lockedUnlockOptionList', 'editEnableDisableOptionList'
    ]);
    moduleValidationServiceSpy = jasmine.createSpyObj('ModuleValidationServiceService', [
      'validateWithEditStateForSingleRecord', 'validateWithUserCountryForSingleRecord'
    ]);
    exceptionServiceSpy = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);
    // Setup subject mocks
    const mockLoadingSubject = new Subject<boolean>();
    const mockRefreshSubject = new Subject<any>();
    const mockTransferSubject = new Subject<boolean>();
    const mockDownloadSubject = new Subject<boolean>();
    const mockCommonLoadingSubject = new Subject<boolean>();

    probeOperationServiceSpy.getProbeDetailLoadingSubject.and.returnValue(mockLoadingSubject);
    probeOperationServiceSpy.getProbeDetailRefreshSubject.and.returnValue(mockRefreshSubject);
    probeOperationServiceSpy.getTransferProbeUISubject.and.returnValue(mockTransferSubject);
    downloadServiceSpy.getisLoadingSubjectForProbDetailPage.and.returnValue(mockDownloadSubject);
    downloadServiceSpy.getdownloadZipFileForProbDetailPageSubject.and.returnValue(mockDownloadSubject);
    commonOperationsServiceSpy.getCommonLoadingSubject.and.returnValue(mockCommonLoadingSubject);

    // Setup API response mocks
    probeApiServiceSpy.getProbeDetailInfo.and.returnValue(of(new HttpResponse({ status: 200, body: mockProbeDetailWithConfig })));
    probeApiServiceSpy.getProbeHistory.and.returnValue(of(new HttpResponse({ status: 200, body: mockProbeHistoryPagableResponse })));
    probeApiServiceSpy.getDeviceListByProbeId.and.returnValue(of(new HttpResponse({ status: 200, body: mockDeviceListResponse })));

    // Setup other service mocks
    commonsServiceSpy.accessDataSizes.and.returnValue(['10', '25', '50']);
    keyValueMappingServiceSpy.enumOptionToList.and.returnValue([]);
    keyValueMappingServiceSpy.lockedUnlockOptionList.and.returnValue([]);
    keyValueMappingServiceSpy.editEnableDisableOptionList.and.returnValue([]);
    commonOperationsServiceSpy.accessProbeOperations.and.returnValue([]);
    featureHistoryDetailServiceSpy.openFeatureHistoryDetailModel.and.returnValue(Promise.resolve(true));

    await TestBed.configureTestingModule({
      declarations: [
        OtsProbesDetailComponent,
        TransferOrderModuleComponent,
        HidePermissionNamePipe,
        EnumMappingDisplayNamePipe,
        BooleanKeyValueMappingDisplayNamePipe,
        CommonBooleanValueDisplayPipe,
        ConfigBaseResponseDisplayPipe,
        FeaturesStartEndDateDisplay,
        PrintListPipe,
        AssignFeaturesComponent,
        AssignConfigCheckBoxPipe,
        AssignConfigDisablePipe,
        FeaturesBaseResponseDisplayPipe,
        FeatureValidityOptionHideShowPipe,
        FeaturesRadioButtonPipe,
        FeaturesCustomEndDateDisplayPipe,
        FeaturesExpireDateDisplayPipe,
        FeaturesValidityPartNumberDisplayPipe
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      imports: [NgbPaginationModule, NgMultiSelectDropDownModule.forRoot(), ReactiveFormsModule, FormsModule, CommonModule],
      providers: [
        { provide: ToastrService, useValue: toastrServiceMock },
        { provide: ProbeApiService, useValue: probeApiServiceSpy },
        { provide: ProbeOperationService, useValue: probeOperationServiceSpy },
        { provide: UpdateFeaturesService, useValue: updateFeaturesServiceSpy },
        { provide: FeatureHistoryDetailService, useValue: featureHistoryDetailServiceSpy },
        { provide: DownloadService, useValue: downloadServiceSpy },
        { provide: CommonOperationsService, useValue: commonOperationsServiceSpy },
        { provide: CommonsService, useValue: commonsServiceSpy },
        { provide: KeyValueMappingServiceService, useValue: keyValueMappingServiceSpy },
        { provide: ModuleValidationServiceService, useValue: moduleValidationServiceSpy },
        { provide: ExceptionHandlingService, useValue: exceptionServiceSpy },
        UpdateAssociationService,
        CustomerAssociationService,
        ConfirmDialogService,
        ValidationService,
        EnumMappingDisplayNamePipe,
        SessionStorageService,
        SSOLoginService,
        RoleApiCallService,
        DatePipe,
        commonsProviders(toastrServiceMock)
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(OtsProbesDetailComponent);
    component = fixture.componentInstance;
    component.probeId = 4211;
    component.resource = ProbDetailResource;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize component properties on ngOnInit', () => {
    component.ngOnInit();

    expect(component.probeDetailDisplay).toBe(true);
    expect(keyValueMappingServiceSpy.enumOptionToList).toHaveBeenCalledWith(ProductStatusEnum);
    expect(keyValueMappingServiceSpy.enumOptionToList).toHaveBeenCalledWith(OSTypeEnum);
    expect(keyValueMappingServiceSpy.lockedUnlockOptionList).toHaveBeenCalled();
    expect(keyValueMappingServiceSpy.editEnableDisableOptionList).toHaveBeenCalled();
    expect(probeApiServiceSpy.getProbeDetailInfo).toHaveBeenCalledWith(4211);
    expect(probeApiServiceSpy.getProbeHistory).toHaveBeenCalled();
    expect(probeApiServiceSpy.getDeviceListByProbeId).toHaveBeenCalled();
    expect(commonsServiceSpy.accessDataSizes).toHaveBeenCalled();
  });

  it('should handle successful probe detail API response', () => {
    component.ngOnInit();

    expect(component.probeDetailWithConfig).toEqual(mockProbeDetailWithConfig);
    expect(component.loading).toBe(false);
    expect(component.probeDetailForTransferProduct).toBeDefined();
  });

  it('should handle probe detail API error', () => {
    const errorResponse = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });
    probeApiServiceSpy.getProbeDetailInfo.and.returnValue(throwError(() => errorResponse));

    component.ngOnInit();

    expect(exceptionServiceSpy.customErrorMessage).toHaveBeenCalledWith(errorResponse);
    expect(component.loading).toBe(false);
  });

  it('should handle probe detail API response with non-200 status', () => {
    probeApiServiceSpy.getProbeDetailInfo.and.returnValue(of(new HttpResponse({ status: 404, body: null })));
    spyOn(component, 'back');

    component.ngOnInit();

    expect(toastrServiceMock.info).toHaveBeenCalledWith(PROBE_DELETE);
    expect(component.back).toHaveBeenCalled();
  });

  it('should handle successful probe history API response', () => {
    component.ngOnInit();

    expect(component.historyList).toEqual([mockProbeHistoryResponse]);
    expect(component.totalHistoryItems).toBe(50);
    expect(component.historyPage).toBe(1);
    expect(component.totalHistories).toBe(50);
    expect(component.totalHistoryDisplay).toBe(10);
  });

  it('should handle probe history API error', () => {
    const errorResponse = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });
    probeApiServiceSpy.getProbeHistory.and.returnValue(throwError(() => errorResponse));

    component.ngOnInit();

    expect(exceptionServiceSpy.customErrorMessage).toHaveBeenCalledWith(errorResponse);
  });

  it('should handle successful device list API response', () => {
    component.ngOnInit();

    expect(component.deviceList).toEqual([mockProbeDeviceList]);
    expect(component.totalItems).toBe(50);
    expect(component.page).toBe(1);
    expect(component.totalDevices).toBe(50);
    expect(component.totalDeviceDisplay).toBe(10);
  });

  it('should handle device list API error', () => {
    const errorResponse = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });
    probeApiServiceSpy.getDeviceListByProbeId.and.returnValue(throwError(() => errorResponse));

    component.ngOnInit();

    expect(exceptionServiceSpy.customErrorMessage).toHaveBeenCalledWith(errorResponse);
  });

  it('should emit showProbe event on back', () => {
    spyOn(component.showProbe, 'emit');

    component.back();

    expect(component.showProbe.emit).toHaveBeenCalled();
  });

  it('should load page when page is different from previous', () => {
    component.previousPage = 1;
    spyOn(component, 'getDeviceListOfProbeId' as any);

    component.loadPage(2);

    expect(component.previousPage).toBe(2);
    expect(component['getDeviceListOfProbeId']).toHaveBeenCalledWith(component.probeId);
  });

  it('should not load page when page is same as previous', () => {
    component.previousPage = 1;
    spyOn(component, 'getDeviceListOfProbeId' as any);

    component.loadPage(1);

    expect(component['getDeviceListOfProbeId']).not.toHaveBeenCalled();
  });

  it('should load feature history page when page is different from previous', () => {
    component.historyPreviousPage = 1;
    spyOn(component, 'getLicenceHistoryListOfProbeId' as any);

    component.loadFeatureHistoryPage(2);

    expect(component.historyPreviousPage).toBe(2);
    expect(component['getLicenceHistoryListOfProbeId']).toHaveBeenCalledWith(component.probeId);
  });

  it('should not load feature history page when page is same as previous', () => {
    component.historyPreviousPage = 1;
    spyOn(component, 'getLicenceHistoryListOfProbeId' as any);

    component.loadFeatureHistoryPage(1);

    expect(component['getLicenceHistoryListOfProbeId']).not.toHaveBeenCalled();
  });

  it('should change data size and reload device list', () => {
    const event = { target: { value: '25' } };
    spyOn(component, 'getDeviceListOfProbeId' as any);

    component.changeDataSize(event);

    expect(component.itemsPerPage).toBe(25);
    expect(component.loading).toBe(true);
    expect(component['getDeviceListOfProbeId']).toHaveBeenCalledWith(component.probeId);
  });

  it('should change feature history data size and reload history list', () => {
    const event = { target: { value: '25' } };
    spyOn(component, 'getLicenceHistoryListOfProbeId' as any);

    component.changeFeatureHistoryDataSize(event);

    expect(component.historyItemsPerPage).toBe(25);
    expect(component.loading).toBe(true);
    expect(component['getLicenceHistoryListOfProbeId']).toHaveBeenCalledWith(component.probeId);
  });

  it('should handle probe operation change', () => {
    const event = { target: { value: ProbeOperationsEnum.LOCK_PROBES } };
    const mockElement = { value: ProbeOperationsEnum.Probe_Operations } as HTMLSelectElement;
    spyOn(document, 'getElementById').and.returnValue(mockElement);

    component.changeProbeOperation(event);

    expect(probeOperationServiceSpy.changeOperationForProbe).toHaveBeenCalledWith(
      ProbeOperationsEnum.LOCK_PROBES,
      ProbDetailResource,
      [component.probeId],
      [component.probeDetailWithConfig]
    );
    expect(mockElement.value).toBe(ProbeOperationsEnum.Probe_Operations);
  });

  it('should open probe connection history', () => {
    component.openProbeConnectionHistory(mockProbeHistoryResponse);

    expect(featureHistoryDetailServiceSpy.openFeatureHistoryDetailModel).toHaveBeenCalledWith(
      FeatureHistoryDetailHeader,
      mockProbeHistoryResponse,
      component.probeId
    );
  });

  it('should transfer probe', async () => {
    probeOperationServiceSpy.transferProbes.and.returnValue(Promise.resolve(true));

    await component.transferProbe();

    expect(probeOperationServiceSpy.transferProbes).toHaveBeenCalledWith(
      [component.probeId],
      [component.probeDetailWithConfig],
      component.resource
    );
  });

  it('should toggle transfer order selection and reload probe detail when probeDetailDisplay is true', () => {
    spyOn(component, 'getProbeDetailInfo' as any);

    component.transferOrderSelectionToggle(true, false);

    expect(component.probeDetailDisplay).toBe(true);
    expect(component.transferOrderSelectionDisaplay).toBe(false);
    expect(component['getProbeDetailInfo']).toHaveBeenCalledWith(component.probeId);
  });

  it('should toggle transfer order selection without reloading when probeDetailDisplay is false', () => {
    spyOn(component, 'getProbeDetailInfo' as any);

    component.transferOrderSelectionToggle(false, true);

    expect(component.probeDetailDisplay).toBe(false);
    expect(component.transferOrderSelectionDisaplay).toBe(true);
    expect(component['getProbeDetailInfo']).not.toHaveBeenCalled();
  });

  it('should refresh connection history', () => {
    spyOn(component, 'getDeviceListOfProbeId' as any);

    component.refreshConnectionHistory();

    expect(component['getDeviceListOfProbeId']).toHaveBeenCalledWith(component.probeId);
  });

  it('should refresh license history', () => {
    spyOn(component, 'getLicenceHistoryListOfProbeId' as any);

    component.refreshLicenseHistory();

    expect(component['getLicenceHistoryListOfProbeId']).toHaveBeenCalledWith(component.probeId);
  });

  it('should refresh probe detail page', () => {
    spyOn(component, 'getProbeDetailInfo' as any);
    spyOn(component, 'getLicenceHistoryListOfProbeId' as any);
    spyOn(component, 'getDeviceListOfProbeId' as any);

    component.refreshProbeDetailPage();

    expect(component['getProbeDetailInfo']).toHaveBeenCalledWith(component.probeId);
    expect(component['getLicenceHistoryListOfProbeId']).toHaveBeenCalledWith(component.probeId);
    expect(component['getDeviceListOfProbeId']).toHaveBeenCalledWith(component.probeId);
  });

  it('should validate with user info when editable is true and country validation passes', () => {
    component.probeDetailWithConfig = { ...mockProbeDetailWithConfig, editable: true, country: 'USA' };
    moduleValidationServiceSpy.validateWithEditStateForSingleRecord.and.returnValue(true);
    moduleValidationServiceSpy.validateWithUserCountryForSingleRecord.and.returnValue(true);

    const result = component['validateWithUserInfo']();

    expect(result).toBe(true);
    expect(moduleValidationServiceSpy.validateWithEditStateForSingleRecord).toHaveBeenCalledWith(true, ProbDetailResource);
    expect(moduleValidationServiceSpy.validateWithUserCountryForSingleRecord).toHaveBeenCalledWith('USA', ProbDetailResource, true);
  });

  it('should validate with user info when editable is false', () => {
    component.probeDetailWithConfig = { ...mockProbeDetailWithConfig, editable: false };
    moduleValidationServiceSpy.validateWithEditStateForSingleRecord.and.returnValue(false);

    const result = component['validateWithUserInfo']();

    expect(result).toBe(false);
    expect(moduleValidationServiceSpy.validateWithEditStateForSingleRecord).toHaveBeenCalledWith(false, ProbDetailResource);
    expect(moduleValidationServiceSpy.validateWithUserCountryForSingleRecord).not.toHaveBeenCalled();
  });

  it('should set loading status and trigger change detection', () => {
    spyOn(component['cdr'], 'detectChanges');

    component['setLoadingStatus'](true);

    expect(component.loading).toBe(true);
    expect(component['cdr'].detectChanges).toHaveBeenCalledTimes(2);
  });

  it('should unsubscribe from all subscriptions on destroy', () => {
    // Setup subscriptions
    component['subscriptionForisloading'] = jasmine.createSpyObj('Subscription', ['unsubscribe']);
    component['subscriptionForDownloadZipFileProbSubject'] = jasmine.createSpyObj('Subscription', ['unsubscribe']);
    component['subscriptionForCommonloading'] = jasmine.createSpyObj('Subscription', ['unsubscribe']);
    component['subscriptionForProbeDetailLoading'] = jasmine.createSpyObj('Subscription', ['unsubscribe']);
    component['subscriptionForProbeDetailRefresh'] = jasmine.createSpyObj('Subscription', ['unsubscribe']);
    component['subscriptionForTransferProbeUI'] = jasmine.createSpyObj('Subscription', ['unsubscribe']);

    component.ngOnDestroy();

    expect(component['subscriptionForisloading'].unsubscribe).toHaveBeenCalled();
    expect(component['subscriptionForDownloadZipFileProbSubject'].unsubscribe).toHaveBeenCalled();
    expect(component['subscriptionForCommonloading'].unsubscribe).toHaveBeenCalled();
    expect(component['subscriptionForProbeDetailLoading'].unsubscribe).toHaveBeenCalled();
    expect(component['subscriptionForProbeDetailRefresh'].unsubscribe).toHaveBeenCalled();
    expect(component['subscriptionForTransferProbeUI'].unsubscribe).toHaveBeenCalled();
  });

  it('should handle undefined subscriptions on destroy', () => {
    component['subscriptionForisloading'] = undefined;
    component['subscriptionForDownloadZipFileProbSubject'] = undefined;
    component['subscriptionForCommonloading'] = undefined;
    component['subscriptionForProbeDetailLoading'] = undefined;
    component['subscriptionForProbeDetailRefresh'] = undefined;
    component['subscriptionForTransferProbeUI'] = undefined;

    expect(() => component.ngOnDestroy()).not.toThrow();
  });

  it('should handle transfer order with TRANSFER_ORDER orderRecordType', () => {
    const transferOrderProbe = { ...mockProbeDetailWithConfig, orderRecordType: TRANSFER_ORDER };
    probeApiServiceSpy.getProbeDetailInfo.and.returnValue(of(new HttpResponse({ status: 200, body: transferOrderProbe })));

    component.ngOnInit();

    expect(commonOperationsServiceSpy.accessProbeOperations).toHaveBeenCalledWith(false, true, true, component.resource);
  });

  it('should handle non-transfer order orderRecordType', () => {
    const nonTransferOrderProbe = { ...mockProbeDetailWithConfig, orderRecordType: 'Regular Order' };
    probeApiServiceSpy.getProbeDetailInfo.and.returnValue(of(new HttpResponse({ status: 200, body: nonTransferOrderProbe })));

    component.ngOnInit();

    expect(commonOperationsServiceSpy.accessProbeOperations).toHaveBeenCalledWith(false, true, false, component.resource);
  });
});
