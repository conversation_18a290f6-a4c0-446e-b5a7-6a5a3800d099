import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { BRIDGE_KIT_MANAGEMANT_DELETE, INTERNAL_SERVER_ERROR } from 'src/app/app.constants';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { KitManagementDetailResponse } from 'src/app/model/KitManagement/KitManagementDetailResponse.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { KitPartnumberDisplayPipe } from 'src/app/shared/pipes/Kit/kitPartnumberDisplay.pipe';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { KitManagemantApiCallService } from 'src/app/shared/Service/KitManagemant/kit-managemant-api-call.service';
import { KitManagementDetailComponent } from './kit-management-detail.component';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';

describe('KitManagementDetailComponent', () => {
  let component: KitManagementDetailComponent;
  let fixture: ComponentFixture<KitManagementDetailComponent>;
  let kitManagemantApiCallServiceSpy: jasmine.SpyObj<KitManagemantApiCallService>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let exceptionHandlingService: ExceptionHandlingService;


  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    const confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['open', 'close']);
    const localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);
    kitManagemantApiCallServiceSpy = jasmine.createSpyObj('KitManagemantApiCallService', ['getKitDetail', 'getKitRevVersion']);

    // Mock the return value of retrieve for the getPermissionObject function
    localStorageServiceMock.retrieve.and.returnValue('mockedPermissionObject');
    await TestBed.configureTestingModule({
      declarations: [KitManagementDetailComponent, KitPartnumberDisplayPipe],
      imports: [],
      providers: [
        SessionStorageService,
        HidePermissionNamePipe,
        KitPartnumberDisplayPipe,
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        { provide: KitManagemantApiCallService, useValue: kitManagemantApiCallServiceSpy },
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(KitManagementDetailComponent);
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  // Test case to ensure that the component handles internal server errors and calls toastrService.error
  it('should handle internal server error and call toastrService.error with INTERNAL_SERVER_ERROR', () => {
    // Arrange: Mock a server error (500 status code) for getKitDetail API
    const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });
    kitManagemantApiCallServiceSpy.getKitDetail?.and.returnValue(throwError(() => mockError));

    // Spy on the exceptionHandlingService to verify if the error is handled properly
    spyOn(exceptionHandlingService, 'customErrorMessage')?.and.callThrough();

    // Act: Trigger the component's initialization (ngOnInit) which should invoke the API and handle the error
    component.ngOnInit();

    // Assert: Verify that the error handling method was called
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();

    // Assert: Ensure toastrService.error is called with an appropriate message for internal server errors
    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);
  });

  // Test case to check handling of an empty response when the API returns a 205 status code
  it('should handle empty response and call toastrService.info with BRIDGE_KIT_MANAGEMANT_DELETE message', () => {
    // Arrange: Mock a response with status 205 and no body (empty response) for getKitDetail API
    kitManagemantApiCallServiceSpy.getKitDetail?.and.returnValue(of(new HttpResponse<KitManagementDetailResponse>({
      body: null,
      status: 205, // Status indicating no content or empty response
      statusText: 'OK',
    })));

    // Act: Trigger the component's initialization (ngOnInit) to invoke the API and handle the empty response
    component.ngOnInit();

    // Assert: Verify that toastrService.info is called with the BRIDGE_KIT_MANAGEMANT_DELETE message
    expect(toastrServiceMock.info).toHaveBeenCalledWith(BRIDGE_KIT_MANAGEMANT_DELETE);
  });

});
