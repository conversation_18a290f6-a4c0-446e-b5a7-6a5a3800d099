import { ComponentFixture, TestBed } from '@angular/core/testing';

import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { INTERNAL_SERVER_ERROR, PROBE_CONFIG_GROUP_DELETE } from 'src/app/app.constants';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { ProbeConfigGroupDeatilResponse } from 'src/app/model/ProbeConfigGroup/ProbeConfigGroupDeatilResponse.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { ProbeConfigGroupApiCallService } from 'src/app/shared/Service/ProbeConfigGroup/probe-config-group-api-call.service';
import { EnumMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';
import { ProbeConfigGroupDetailComponent } from './probe-config-group-detail.component';

describe('ProbeConfigGroupDetailComponent', () => {
  let component: ProbeConfigGroupDetailComponent;
  let fixture: ComponentFixture<ProbeConfigGroupDetailComponent>;
  let probeConfigGroupApiCallServiceSpy: jasmine.SpyObj<ProbeConfigGroupApiCallService>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let exceptionHandlingService: ExceptionHandlingService;


  beforeEach(async () => {
    const localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);
    const confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['open', 'close']);
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    probeConfigGroupApiCallServiceSpy = jasmine.createSpyObj('ProbeConfigGroupApiCallService', ['deleteProbeConfigGroup', 'getProbeConfigGroupDetails']);

    localStorageServiceMock.retrieve.and.returnValue('mockedPermissionObject');
    await TestBed.configureTestingModule({
      declarations: [ProbeConfigGroupDetailComponent, EnumMappingDisplayNamePipe],
      imports: [],
      providers: [
        SessionStorageService,
        HidePermissionNamePipe,
        PrintListPipe,
        EnumMappingDisplayNamePipe,
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        { provide: ProbeConfigGroupApiCallService, useValue: probeConfigGroupApiCallServiceSpy },
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(ProbeConfigGroupDetailComponent);
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call toastrService.error with INTERNAL_SERVER_ERROR', () => {
    // **Arrange: Mock dependencies and set up the test environment**
    // Create a mock HttpErrorResponse to simulate a server error
    const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });

    // Mock the API call to return an error response
    probeConfigGroupApiCallServiceSpy.getProbeConfigGroupDetails?.and.returnValue(throwError(() => mockError));

    // Spy on the `CustomerrorMessage` method of `exceptionHandlingService` to verify error handling
    spyOn(exceptionHandlingService, 'customErrorMessage')?.and.callThrough();

    // **Act: Trigger the component's logic**
    // Initialize the component, which invokes `getProbeConfigGroupDetails` internally
    component.ngOnInit();

    // **Assert: Verify error handling behavior**
    // Ensure the `CustomerrorMessage` method was called to process the error
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();

    // Ensure the toastr service displays an error message for `INTERNAL_SERVER_ERROR`
    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);
  });


  it('should call toastrService.info with getProbeConfigGroupDetails for empty response', () => {
    // **Arrange: Mock dependencies and set up the test environment**
    // Mock the API call to return an empty response with HTTP status 205
    probeConfigGroupApiCallServiceSpy.getProbeConfigGroupDetails?.and.returnValue(of(new HttpResponse<ProbeConfigGroupDeatilResponse>({
      body: null,// Simulate an empty response body
      status: 205, // Mock HTTP status indicating a specific response
      statusText: 'OK',
    }))
    );

    // **Act: Trigger the component's logic**
    // Initialize the component, which invokes `getProbeConfigGroupDetails` internally
    component.ngOnInit();

    // **Assert: Verify the behavior for empty responses**
    // Ensure the toastr service displays an informational message for empty responses
    expect(toastrServiceMock.info).toHaveBeenCalledWith(PROBE_CONFIG_GROUP_DELETE);
  });

});

