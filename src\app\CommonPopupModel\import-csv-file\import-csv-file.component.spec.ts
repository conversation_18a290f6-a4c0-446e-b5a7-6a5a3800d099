import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { of, throwError } from 'rxjs';
import { ListKitManagementResource, OTSKitManagementListResource } from 'src/app/app.constants';
import { ContentInput } from 'src/app/model/importCSVFile/ImportFileContentInput.model';
import { ImportFilePopupInput } from 'src/app/model/importCSVFile/ImportFilePopupInput.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { ImportCsvFileApiService } from 'src/app/shared/importFileService/import-csv-file-api.service';
import { SubjectMessageService } from 'src/app/shared/subject-message.service';
import { DownloadService } from 'src/app/shared/util/download.service';
import { ImportCsvFileComponent } from './import-csv-file.component';

describe('ImportCsvFileComponent', () => {
  let component: ImportCsvFileComponent;
  let fixture: ComponentFixture<ImportCsvFileComponent>;
  let mockActiveModal: jasmine.SpyObj<NgbActiveModal>;
  let mockExceptionService: jasmine.SpyObj<ExceptionHandlingService>;
  let mockDownloadService: jasmine.SpyObj<DownloadService>;
  let mockImportCsvFileApiService: jasmine.SpyObj<ImportCsvFileApiService>;
  let mockSubjectMessageService: jasmine.SpyObj<SubjectMessageService>;

  beforeEach(async () => {
    mockActiveModal = jasmine.createSpyObj('NgbActiveModal', ['close']);
    mockExceptionService = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);
    mockDownloadService = jasmine.createSpyObj('DownloadService', ['downloadTemplate']);
    mockImportCsvFileApiService = jasmine.createSpyObj('ImportCsvFileApiService', ['downloadCsvTemplate', 'importFileForUpdateTemplateData']);
    mockSubjectMessageService = jasmine.createSpyObj('SubjectMessageService', ['setLoading']);

    await TestBed.configureTestingModule({
      declarations: [ImportCsvFileComponent],
      providers: [
        { provide: NgbActiveModal, useValue: mockActiveModal },
        { provide: ExceptionHandlingService, useValue: mockExceptionService },
        { provide: DownloadService, useValue: mockDownloadService },
        { provide: ImportCsvFileApiService, useValue: mockImportCsvFileApiService },
        { provide: SubjectMessageService, useValue: mockSubjectMessageService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ImportCsvFileComponent);
    component = fixture.componentInstance;

    const contentInput: ContentInput = {
      title: 'Content Title',
      messageList: ['Message 1', 'Message 2']
    };

    component.importFilePopupInput = new ImportFilePopupInput(
      'Import CSV File',
      'Please upload a CSV file',
      true,
      'template.csv',
      contentInput,
      'Cancel',
      'your_resource'
    );

    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should download the CSV template successfully', () => {
    const mockResponse = new HttpResponse({ body: 'csv content', status: 200 });
    mockImportCsvFileApiService.downloadCsvTemplate.and.returnValue(of(mockResponse));

    component.downloadUpdateTemplate();

    expect(mockSubjectMessageService.setLoading).toHaveBeenCalledWith(true);
    expect(mockDownloadService.downloadTemplate).toHaveBeenCalledWith('template.csv', mockResponse);
    expect(mockSubjectMessageService.setLoading).toHaveBeenCalledWith(false);
  });

  it('should handle error during template download', () => {
    const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });
    mockImportCsvFileApiService.downloadCsvTemplate.and.returnValue(throwError(() => mockError));

    component.downloadUpdateTemplate();

    expect(mockSubjectMessageService.setLoading).toHaveBeenCalledWith(true);
    expect(mockExceptionService.customErrorMessage).toHaveBeenCalledWith(mockError);
    expect(mockSubjectMessageService.setLoading).toHaveBeenCalledWith(false);
  });

  it('should validate non-csv file extension', () => {
    const file = new File(['content'], 'data.txt', { type: 'text/plain' });
    component.onUpdateFileSelect([file]);

    expect(component.updateFileValidation).toBe('Only csv is supported.');
    expect(component.updateFileValidationNotes).toBeNull();
  });

  it('should validate incorrect file name format for OTSKitManagementListResource', () => {
    component.importFilePopupInput.resourse = OTSKitManagementListResource;
    const file = new File(['content'], 'invalidname.csv', { type: 'text/csv' });
    component.onUpdateFileSelect([file]);

    expect(component.updateFileValidation).toBe('Invalid file name.');
    expect(component.updateFileValidationNotes).toBe('Please consider this as an example: D008855AA_Kosmos Ots Price List.csv');
  });

  it('should validate incorrect file name format for ListKitManagementResource', () => {
    component.importFilePopupInput.resourse = ListKitManagementResource;
    const file = new File(['content'], 'invalidname.csv', { type: 'text/csv' });
    component.onUpdateFileSelect([file]);

    expect(component.updateFileValidation).toBe('Invalid file name.');
    expect(component.updateFileValidationNotes).toBe('Please consider this as an example: D008855AA_Kosmos Bridge Price List.csv');
  });

  it('should process valid CSV file successfully', () => {
    const file = new File(['content'], 'D008855AA_Kosmos Ots Price List.csv', { type: 'text/csv' });
    const mockResponse = new HttpResponse({
      body: null,
      status: 200
    });
    mockImportCsvFileApiService.importFileForUpdateTemplateData.and.returnValue(of(mockResponse));

    component.onUpdateFileSelect([file]);

    expect(component.updateFileValidation).toBeNull();
    expect(component.updateFileValidationNotes).toBeNull();
    expect(component.updateFileSuccessStatus).toEqual(mockResponse.body);
    expect(mockSubjectMessageService.setLoading).toHaveBeenCalledWith(true);
    expect(mockSubjectMessageService.setLoading).toHaveBeenCalledWith(false);
  });

  it('should handle 412 error during file import', () => {
    const file = new File(['content'], 'D008855AA_Kosmos Ots Price List.csv', { type: 'text/csv' });
    const mockError = new HttpErrorResponse({
      status: 412,
      error: { errorMessage: 'Validation failed' }
    });
    mockImportCsvFileApiService.importFileForUpdateTemplateData.and.returnValue(throwError(() => mockError));

    component.onUpdateFileSelect([file]);

    expect(component.updateFileErrorStatus).toBe('Validation failed');
    expect(mockSubjectMessageService.setLoading).toHaveBeenCalledWith(true);
    expect(mockSubjectMessageService.setLoading).toHaveBeenCalledWith(false);
  });

  it('should handle non-412 error during file import', () => {
    const file = new File(['content'], 'D008855AA_Kosmos Ots Price List.csv', { type: 'text/csv' });
    const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });
    mockImportCsvFileApiService.importFileForUpdateTemplateData.and.returnValue(throwError(() => mockError));

    component.onUpdateFileSelect([file]);

    expect(mockExceptionService.customErrorMessage).toHaveBeenCalledWith(mockError);
    expect(mockSubjectMessageService.setLoading).toHaveBeenCalledWith(true);
    expect(mockSubjectMessageService.setLoading).toHaveBeenCalledWith(false);
  });

  it('should close the modal on decline', () => {
    component.decline();
    expect(mockActiveModal.close).toHaveBeenCalledWith(false);
  });

  it('should display validation error in template', () => {
    component.updateFileValidation = 'Only csv is supported.';
    fixture.detectChanges();

    const validationElement = fixture.debugElement.query(By.css('.validation'));
    expect(validationElement.nativeElement.textContent).toContain('Only csv is supported.');
  });

  it('should display error message in template', () => {
    component.updateFileErrorStatus = 'Validation failed';
    fixture.detectChanges();

    const errorElement = fixture.debugElement.query(By.css('.validation'));
    expect(errorElement.nativeElement.textContent).toContain('Validation failed');
  });

  it('should call validateFileName and return false when file has invalid revision format', async () => {
    component.importFilePopupInput.resourse = ListKitManagementResource; // or 'OTSKitManagementListResource'

    // This file name has digits before `_` and a suffix of 'AA' after the digits — it triggers the last return false path.
    const file = new File(['dummy content'], 'D008855AA_Kosmos Bridge Price List.csv', { type: 'text/csv' });

    component.onUpdateFileSelect([file]);

    fixture.detectChanges();
    await fixture.whenStable();

    expect(component.updateFileValidationNotes).toBeNull();
  });

});
