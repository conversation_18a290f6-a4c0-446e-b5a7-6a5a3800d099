import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { RoleApiCallService } from 'src/app/shared/Service/RoleService/role-api-call.service';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { SalesOrderErrorFilterComponent } from './sales-order-error-filter.component';
import { ToastrService } from 'ngx-toastr';
import { EnumMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';

describe('SalesOrderErrorFilterComponent', () => {
  let component: SalesOrderErrorFilterComponent;
  let fixture: ComponentFixture<SalesOrderErrorFilterComponent>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);

    await TestBed.configureTestingModule({
      declarations: [SalesOrderErrorFilterComponent],
      imports: [ReactiveFormsModule],
      providers: [
        CommonsService,
        CommonOperationsService,
        RoleApiCallService,
        ExceptionHandlingService,
        AuthJwtService,
        EnumMappingDisplayNamePipe,
        PrintListPipe,
        SessionStorageService,
        HidePermissionNamePipe,
        LocalStorageService,
        ConfirmDialogService,
        commonsProviders(toastrServiceMock),

      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(SalesOrderErrorFilterComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
