import { HttpClient, HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { firstValueFrom, Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Cancel, Confirm } from 'src/app/app.constants';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { SuccessMessageResponse } from 'src/app/model/common/SuccessMessageResponse.model';
import { RolePageResponse } from 'src/app/model/Role/rolePageResponse.model';
import { RolePermissionResponse } from 'src/app/model/Role/rolePermissionResponse.model';
import { RoleReqeest } from 'src/app/model/Role/roleRequest.model';
import { RoleRequestBody } from 'src/app/model/Role/roleRequestBody.model';
import { RoleResponse } from 'src/app/model/Role/roleResponse.model';
import { PermissionAction } from '../../enum/Permission/permissionAction.enum';
import { ExceptionHandlingService } from '../../ExceptionHandling.service';
import { ConfigInjectService } from '../../InjectService/config-inject.service';
import { PermissionService } from '../../permission.service';
import { HidePermissionNamePipe } from '../../pipes/Role/hidePermissionName.pipe';
import { CommonsService } from '../../util/commons.service';
import { createRequestOption } from '../../util/request-util';
import { RoleService } from './role.service';

@Injectable({
  providedIn: 'root'
})
export class RoleApiCallService {

  private serverApiUrl = this.configInjectService.getServerApiUrl();
  private roleBaseUrl: string = this.serverApiUrl + "api/";
  private rolePostfix = "role";
  private permissionPostfix = "permission";

  constructor(
    private configInjectService: ConfigInjectService,
    private http: HttpClient,
    private roleService: RoleService,
    private exceptionService: ExceptionHandlingService,
    private toste: ToastrService,
    private confirmDialogService: ConfirmDialogService,
    private permissionService: PermissionService,
    private commonsService: CommonsService,
    private hidePermissionNamePipe: HidePermissionNamePipe) { }


  /**
   * Get Role Permission List
   * <AUTHOR>
   * @param resourceName 
   * @returns 
   */
  public async getRolePermissionList(resourceName: string): Promise<Array<RolePermissionResponse>> {
    if (this.permissionService.getPermissionForPermissionList(PermissionAction.GET_PERMISSIONLIST_ACTION)) {
      let permission: Array<RolePermissionResponse> = [];
      this.roleService.isLoading(true, resourceName);
      try {
        const res: HttpResponse<RolePermissionResponse[]> = await firstValueFrom(
          this.http.get<RolePermissionResponse[]>(`${this.roleBaseUrl}${this.permissionPostfix}`, { observe: 'response' })
        );
        permission = this.hidePermissionNamePipe.transform(res.body);
      } catch (error) {
        if (error instanceof HttpErrorResponse) {
          this.exceptionService.customErrorMessage(error);
        }
        permission = [];
      } finally {
        this.roleService.isLoading(false, resourceName);
      }
      return permission;
    } else {
      return [];
    }
  }

  /**
   * Create Role
   * <AUTHOR>
   * @param roleResponse 
   * @returns 
   */
  public createRole(roleResponse: RoleReqeest): Observable<HttpResponse<SuccessMessageResponse>> {
    return this.http.post<SuccessMessageResponse>(this.roleBaseUrl + this.rolePostfix, roleResponse, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
   * Update Role
   * <AUTHOR>
   * @param roleId 
   * @param roleResponse 
   * @returns 
   */
  public updateRole(roleId: number, roleResponse: RoleReqeest): Observable<HttpResponse<SuccessMessageResponse>> {
    return this.http.put<SuccessMessageResponse>(this.roleBaseUrl + this.rolePostfix + "/" + roleId, roleResponse, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
   * Delete role
   * <AUTHOR>
   * @param roleId 
   * @returns 
   */
  public deleteRole(roleId: number[]): Observable<HttpResponse<SuccessMessageResponse>> {
    return this.http.delete<SuccessMessageResponse>(this.roleBaseUrl + this.rolePostfix + "/" + roleId, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
   * Get Role Detail
   * <AUTHOR>
   * @param roleId 
   * @returns 
   */
  public getRoleDetail(roleId: number): Observable<HttpResponse<RoleResponse>> {
    return this.http.get<RoleResponse>(this.roleBaseUrl + this.rolePostfix + "/" + roleId, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
   * Get Role List 
   * <AUTHOR>
   * @param requestBody 
   * @param req 
   * @returns 
   */
  public getRoleList(requestBody: RoleRequestBody, req: any): Observable<HttpResponse<RolePageResponse>> {
    const options = createRequestOption(req);
    return this.http.post<RolePageResponse>(this.roleBaseUrl + this.rolePostfix + "/search", requestBody, { params: options, observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }


  /**
   * Get Role Name List
   * <AUTHOR>
   * @returns 
   */
  public async getRoleNameList(): Promise<string[]> {
    if (this.permissionService.getRolePermission(PermissionAction.GET_ROLE_NAME_LIST_ACTION)) {
      let permission: Array<string> = [];
      try {
        const res: HttpResponse<string[]> = await firstValueFrom(
          this.http.get<string[]>(`${this.roleBaseUrl}${this.rolePostfix}/names`, { observe: 'response' })
        );
        permission = res.body;
      } catch (error) {
        if (error instanceof HttpErrorResponse) {
          this.exceptionService.customErrorMessage(error);
        }
        permission = [];
      }
      return permission;
    } else {
      return [];
    }
  }

  /**
   * Delete Role
   * <AUTHOR>
   * @param roleId 
   * @param resourceName 
   */
  public deleteRoleAction(roleId: number[], resourceName: string, isFilterHidden: boolean): void {
    this.roleService.isLoading(true, resourceName);
    this.deleteRole(roleId).subscribe({
      next: (res: any) => {
        let listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, true, true);
        this.roleModifySuccessAfterAction(listingPageReloadSubjectParameter, res.body.message, resourceName, isFilterHidden);
      }, error: (error: HttpErrorResponse) => {
        this.roleModifyErrorAfterAction(resourceName, error);
      }
    });
  }

  /**
   * delete Role model open
   * <AUTHOR>
   * @param id 
   * @param resourceName 
   * @param isFilterHidden 
   */
  public deleteRoleConfirmation(id: number[], resourceName: string, isFilterHidden: boolean): void {
    this.confirmDialogService.confirm("Are you sure?", "Are you sure you want to delete the Role(s)?", Confirm, Cancel).then((res: boolean) => {
      if (res) {
        this.deleteRoleAction(id, resourceName, isFilterHidden);
      }
    }).finally(() => { });
  }

  /**
   * Role Add , Update and Delete SuccessFully after Action
   * <AUTHOR>
   * @param isReloadData
   * @param message 
   * @param resourceName 
   */
  public roleModifySuccessAfterAction(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, message: string, resourceName: string, isFilterHidden: boolean): void {
    this.toste.success(message);
    this.roleService.callRefreshPageSubject(listingPageReloadSubjectParameter, resourceName, isFilterHidden);
  }

  /**
   * Role Add,Update and Delete Exception after Action
   * <AUTHOR>
   * @param resourceName 
   * @param error 
   */
  public roleModifyErrorAfterAction(resourceName: string, error: HttpErrorResponse): void {
    this.roleService.isLoading(false, resourceName);
    if (error.status == 412) {
      this.toste.info("Selected role cannot be deleted as it is assigned to an existing user");
    } else {
      this.exceptionService.customErrorMessage(error);
    }
  }


}
