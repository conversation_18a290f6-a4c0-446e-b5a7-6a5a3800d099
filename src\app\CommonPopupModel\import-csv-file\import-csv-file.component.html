<!-- model header start -->
<div class="modal-header">
  <label class="modal-title">{{ importFilePopupInput?.title }}</label>
</div>
<!-- model header end -->
<!-- model body start -->
<div class="modal-body">
  <div class="row importUsers">
    <!-- heading start -->
    <div class="col-12 col-md-12 importButton"
      *ngIf="importFilePopupInput?.subTitle != null || !importFilePopupInput?.isDownloadTemplate">
      <span class="mr-0">{{importFilePopupInput?.subTitle}}</span>
      <a class="downloadTemplateLink" *ngIf="importFilePopupInput?.isDownloadTemplate" id="downloadTemplateLink"
        (click)="downloadUpdateTemplate()">Download
        template</a>
    </div>
    <div class="import-csv-mainDiv">
      <!-- heading end -->
      <div class="col-md-12">
        <!------------------------------------------------>
        <!--------------Message List Display-------------->
        <!------------------------------------------------>
        <div class="importButton" *ngIf="importFilePopupInput?.content != null">
          <span class="boldText">{{importFilePopupInput?.content.title}}</span><br />
          <ol>
            <ng-template ngFor let-message [ngForOf]="importFilePopupInput?.content.messageList">
              <li><span class="listText">{{message}}</span></li>
            </ng-template>
          </ol>
        </div>
        <!------------------------------------------->
        <!------------------------------------------->
        <!-- import csv div start -->
        <div class="d-flex">
          <input style="display: none;" class="btn btn-sm btn-primary show-toast" type="file" #file name="csvLocation"
            id="userCsvFile" accept=".csv" placeholder="csv file location"
            (change)="onUpdateFileSelect($any($event.target)?.files)" />
          <label class="btn importLabel btn-orange Pointer" for="userCsvFile">Import CSV</label>
        </div>
        <!-- import csv div end -->
        <div *ngIf="updateFileValidation!=null">
          <span class="fa fa-exclamation-circle validationFailImg mr-1"></span>
          <small class="validation">{{ updateFileValidation}}</small><br />
          <small class="validation ml-3" *ngIf="updateFileValidationNotes!=null">{{ updateFileValidationNotes}}</small>
        </div>
        <!-- error div start -->
        <div *ngIf="updateFileSuccessStatus!=null">
          <span
            [className]="!updateFileSuccessStatus.success?'fa fa-exclamation-circle validationFailImg mr-1':'fa fa-check-circle validationSuccessImg mr-1'"></span>
          <small [className]="!updateFileSuccessStatus.success?'validation':'success'">
            {{updateFileSuccessStatus.message }} </small>
        </div>
        <!-- error div end -->
        <!-- error div start -->
        <div *ngIf="updateFileErrorStatus!=null">
          <span class="fa fa-exclamation-circle validationFailImg mr-1"></span>
          <small class="validation">{{
            updateFileErrorStatus }}</small>
        </div>
        <!-- error div start -->
      </div>
      <!-- result division start -->
      <div class="col-md-12 mt-4" *ngIf="updateFileSuccessStatus!=null && updateFileSuccessStatus?.errors?.length>0">
        <div class="table table-responsive table-striped import-error-table mb-0">
          <!-- error table start -->
          <table aria-hidden="true">
            <!-- table heading start -->
            <tr>
              <th style="width: 15%;" class="text_nowrap">Row Number</th>
              <th style="width: 35%;" class="text_nowrap">{{importFilePopupInput.resourse|showImportHeader}}</th>
              <th style="width: 50%;" class="text_nowrap">Error Message</th>
            </tr>
            <!-- table heading end -->
            <!-- error data start -->
            <tr *ngFor="let error of updateFileSuccessStatus.errors">
              <td style="width: 23%;">{{ error.rowNumber }}</td>
              <td style="width: 27%;" class="breakWord">{{ error.name }}</td>
              <td style="width: 50%;">
                <ul class="ULPadding">
                  <li *ngFor="let errmsg of error.errorMessage">{{ errmsg }}<br></li>
                </ul>
              </td>
            </tr>
            <!-- error data end -->
          </table>
          <!-- error table end -->
        </div>
      </div>
      <!-- result division end -->
    </div>
  </div>
</div>
<!-- model body end -->
<!-- model footer start -->
<div class="modal-footer px-0">
  <button type="button" class="btn btn-sm btn-orange mr-0" (click)="decline()" id="cancelButtonCSV">{{
    importFilePopupInput.btnCancelText
    }}</button>
</div>
<!-- model footer end -->