import { HttpBackend, HttpClient, HttpHead<PERSON>, HttpResponse } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { LocalStorageService } from 'ngx-webstorage';
import { Observable } from 'rxjs';
import { createRequestOption } from '../../app/shared/util/request-util';
import { RdmUserId } from '../app.constants';
import { Inventory } from '../model/inventory.model';
import { ItemHistory } from '../model/item-history.model';
import { UploadPackageRequest } from '../model/upload.package.request';
import { UploadPackageResponse } from '../model/upload.package.response';
import { API_BASE_URL } from './config';
import { SuccessMessageResponse } from '../model/common/SuccessMessageResponse.model';
import { SearchInventoryRequest } from '../model/searchInventoryRequest';
import { AttachmentDownloadResponseModel } from '../model/SoftwaarBuilds/attachment-download-response.model';

type EntityArrayResponseType = HttpResponse<Inventory[]>;

@Injectable({
  providedIn: 'root'
})
export class InventoryService {

  public resourceUrlList = this.SERVER_API_URL + 'api/software-builds';
  public resourceUrl = this.SERVER_API_URL + 'api/inventory';
  private resourceUrlForHistory = this.SERVER_API_URL + 'api/fusionItems';

  private https: HttpClient;

  constructor(protected http: HttpClient, private localStorage: LocalStorageService, @Inject(API_BASE_URL) public SERVER_API_URL: string, httpBackend: HttpBackend) { this.https = new HttpClient(httpBackend); }

  inventoryList(filterData: SearchInventoryRequest, req?: any): Observable<EntityArrayResponseType> {
    const options = createRequestOption(req);
    return this.http.post<Inventory[]>(this.resourceUrlList + '/search', filterData, { params: options, observe: 'response' });
  }

  getItemHistory(id: number): Observable<HttpResponse<ItemHistory[]>> {
    let uid = this.localStorage.retrieve(RdmUserId);
    let headers = new HttpHeaders();
    headers = headers.set('userId', uid);
    return this.http.get<ItemHistory[]>(this.resourceUrlForHistory + '/history/' + id, { observe: 'response', headers: headers });
  }

  deleteSoftwearBuild(id: number): Observable<HttpResponse<SuccessMessageResponse>> {
    return this.http.delete<SuccessMessageResponse>(this.resourceUrl + '/' + id, { observe: 'response' });
  }

  getAttachmentUrl(id: number, prm?: any): Observable<HttpResponse<AttachmentDownloadResponseModel>> {
    const options = createRequestOption(prm);
    return this.http.get<AttachmentDownloadResponseModel>(this.resourceUrl + '/getSignedURL/' + id, { params: options, observe: 'response' });
  }

  pushFileToStorage(requestData: UploadPackageRequest, prm?: any): Observable<HttpResponse<UploadPackageResponse>> {
    const options = createRequestOption(prm);
    return this.http.post<UploadPackageResponse>(this.resourceUrl + '/upload', requestData, { params: options, observe: 'response' });
  }

  updateFirmwareUploadStatus(requestData: UploadPackageRequest, prm?: any) {
    const options = createRequestOption(prm);
    return this.http.put(this.resourceUrl + '/upload', requestData, { params: options, observe: 'response' });
  }

  /**
   * Upload avatar image
   * 
   * <AUTHOR>
   * @param file 
   * @param fileUrl 
   */
  public uploadFileToStorage(file: File, fileUrl: string): Observable<HttpResponse<any>> {
    let headers = new HttpHeaders();
    headers = headers.set('x-ms-blob-type', 'BlockBlob');
    return this.https.put(fileUrl, file, { observe: 'response', headers: headers });
  }

  /**
   * Upload avatar image's final call
   * 
   * <AUTHOR>
   * @param fileData 
   * @param fileUrl 
   * @param fileType 
   */
  public commitFileToStorage(fileData: any, fileUrl: string, fileType: string): Observable<HttpResponse<any>> {
    let headers = new HttpHeaders();
    headers = headers.set('x-ms-blob-content-type', fileType);
    return this.https.put(fileUrl, fileData, { observe: 'response', headers: headers });
  }

  /**
   * Map Inventories with Device Type 
   * 
   * <AUTHOR>
   * @param inventoryId 
   * @param prm 
   */
  public mapInventoryWithDeviceType(inventoryId: number[], prm?: any): Observable<HttpResponse<SuccessMessageResponse>> {
    const options = createRequestOption(prm);
    return this.http.put<SuccessMessageResponse>(this.resourceUrl + '/' + inventoryId, {}, { params: options, observe: 'response' });
  }

  /**
   * Mark Inventories as Active/Inactive 
   * 
   * <AUTHOR>
   * @param inventoryId 
   * @param prm 
   */
  public markInventoriesActiveInactive(inventoryId: number[], prm?: any): Observable<HttpResponse<SuccessMessageResponse>> {
    const options = createRequestOption(prm);
    return this.http.put<SuccessMessageResponse>(this.resourceUrl + '/' + inventoryId, {}, { params: options, observe: 'response' });
  }

  public updateInventory(inventoryId: number, prm: any): Observable<HttpResponse<SuccessMessageResponse>> {
    return this.http.put<SuccessMessageResponse>(this.resourceUrl + '/' + inventoryId, prm, { observe: 'response' });
  }

}
