import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { commonsProviders, testErrorHandling } from 'src/app/Tesing-Helper/test-utils';
import { INTERNAL_SERVER_ERROR, ROLE_DELETE } from 'src/app/app.constants';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { RoleResponse } from 'src/app/model/Role/roleResponse.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { RoleApiCallService } from 'src/app/shared/Service/RoleService/role-api-call.service';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { GetPermissionModuleName } from 'src/app/shared/pipes/Role/getPermissionModuleName.pipe';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { RoleDetailComponent } from './role-detail.component';
import { EnumMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';

describe('RoleDetailComponent', () => {
  let component: RoleDetailComponent;
  let fixture: ComponentFixture<RoleDetailComponent>;
  let localStorageServiceMock: jasmine.SpyObj<LocalStorageService>;
  let exceptionHandlingService: ExceptionHandlingService;
  let roleApiCallService: RoleApiCallService;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;

  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    const confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['open', 'close']);

    localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);

    // Mock the return value of retrieve for the getPermissionObject function
    localStorageServiceMock.retrieve.and.returnValue('mockedPermissionObject');
    await TestBed.configureTestingModule({
      declarations: [RoleDetailComponent, PrintListPipe, GetPermissionModuleName],
      imports: [],
      providers: [
        CommonOperationsService,
        RoleApiCallService,
        CommonsService,
        ExceptionHandlingService,
        EnumMappingDisplayNamePipe,
        PrintListPipe,
        AuthJwtService,
        SessionStorageService,
        HidePermissionNamePipe,
        GetPermissionModuleName,
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(RoleDetailComponent);
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService);
    roleApiCallService = TestBed.inject(RoleApiCallService);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call toastrService.error with INTERNAL_SERVER_ERROR', async () => {
    // **Arrange: Mock dependencies and set up the test environment**
    // Create a mock HttpErrorResponse to simulate a server error
    const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });

    // Mock the API call to return an error response
    spyOn(roleApiCallService, 'getRoleDetail')?.and.returnValue(throwError(() => mockError));

    // Spy on the `CustomerrorMessage` method of `exceptionHandlingService` to verify error handling
    spyOn(exceptionHandlingService, 'customErrorMessage')?.and.callThrough();

    // **Act: Trigger the component's logic**
    // Initialize the component, which invokes `getSalesOrderDetails` internally`
    component.ngOnInit();

    // **Act: Trigger Angular lifecycle methods and finalize change detection**
    fixture.detectChanges(); // Run initial change detection
    await fixture.whenStable(); // Wait for asynchronous operations to complete
    // **Assert: Verify error handling behavior**
    // Ensure the `CustomerrorMessage` method was called to process the error
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();

    // Ensure the toastr service displays an error message for `INTERNAL_SERVER_ERROR`
    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);

    testErrorHandling(roleApiCallService.getRoleDetail, () => component.ngOnInit(), exceptionHandlingService, toastrServiceMock, fixture);

  });

  it('should call toastrService.error with No Content', async () => {

    // Mock the API call to return an response
    spyOn(roleApiCallService, 'getRoleDetail')?.and.returnValue(of(new HttpResponse<RoleResponse>({
      body: null, // Mocked response data
      status: 204, // Simulate a successful API response
      statusText: 'No Content'
    })));
    // **Act: Trigger the component's logic**
    // Initialize the component, which invokes `getSalesOrderDetails` internally
    component.ngOnInit();

    // **Act: Trigger Angular lifecycle methods and finalize change detection**
    fixture.detectChanges(); // Run initial change detection
    await fixture.whenStable(); // Wait for asynchronous operations to complete

    // Ensure the toastr service displays an error message for `ROLE_DELETE`
    expect(toastrServiceMock.info).toHaveBeenCalledWith(ROLE_DELETE);
  });
});
