import { Component, Input, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-delete-inventory-dialog',
  templateUrl: './delete-inventory-dialog.component.html',
  styleUrls: ['./delete-inventory-dialog.component.css']
})
export class DeleteInventoryDialogComponent implements OnInit {

  @Input() title: string;
  @Input() message: string;
  @Input() btnOkText: string;
  @Input() btnCancelText: string;
  @Input() itemNumber: string;
  @Input() msgExtention: string;

  constructor(private activeModal: NgbActiveModal) { }

  ngOnInit() {
    this.message = this.message + ' ' + this.itemNumber + '? ' + this.msgExtention;
  }

  public decline() {
    this.activeModal.close(false);
  }

  public accept() {
    this.activeModal.close(true);
  }

  public dismiss() {
    this.activeModal.dismiss();
  }

}
