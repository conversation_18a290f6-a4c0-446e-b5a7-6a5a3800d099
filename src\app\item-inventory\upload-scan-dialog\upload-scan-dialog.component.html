<!-- model header start -->
<div class="modal-header">
    <!-- model title start -->
    <label class="modal-title">{{ title }}</label>
    <!-- model title end -->
</div>
<!-- model header end -->
<!-- model body start -->
<div class="modal-body" id="uploadScanDialog">
    <div>
        <!-- upload form start -->
        <form enctype="multipart/form-data" name='fileinfo' [formGroup]="form">
            <!-- table start -->
            <table aria-hidden="true">
                <!-- title row start -->
                <tr>
                    <!-- title header start -->
                    <th class="upload-title" id="title">
                        <span>Title</span>
                    </th>
                    <!-- title header end -->
                    <td>
                        <!-- title input start -->
                        <input type="text" class="upload-form-control" tabindex="-1" id="title" name="title"
                            formControlName="Title" (keyup)="buttonDisable($any($event.target)?.value)" required>
                        <!-- title input end -->
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td>
                        <!-- validation for title start -->
                        <div
                            *ngIf="(form.get('Title').touched || form.get('Title').dirty) && form.get('Title').invalid ">
                            <div *ngIf="form.get('Title').errors['required'] || form.get('Title').errors['pattern'] ">
                                <span class="validation">Enter Valid Title</span>
                            </div>
                            <div *ngIf="form.get('Title').errors['maxlength']">
                                <span class="validation">{{textBoxMaxCharactersAllowedMessage}}</span>
                            </div>
                        </div>
                        <!-- validation for title end -->
                    </td>
                </tr>
                <!-- title row end -->
                <!-- version row start -->
                <tr>
                    <!-- version start -->
                    <th class="upload-title" id="version">
                        <span>Version</span>
                    </th>
                    <!-- version end -->
                    <td>
                        <!-- version input start -->
                        <input type="text" class="upload-form-control mt-2" tabindex="-1" id="version" name="version"
                            placeholder="1.1.1.1" formControlName="Version"
                            (keyup)="buttonDisable($any($event.target)?.value)" required>
                        <!-- version input end -->
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td>
                        <!-- validation for version start -->
                        <div
                            *ngIf="(form.get('Version').touched || form.get('Version').dirty) && form.get('Version').invalid ">
                            <div *ngIf="form.get('Version').errors['required'] || form.get('Version').invalid">
                                <span class="validation">Enter Valid Version</span>
                            </div>
                        </div>
                        <!-- validation for version end -->
                    </td>
                </tr>
                <!-- version row end -->
                <!-- Country row start -->
                <tr>
                    <!-- Country header start -->
                    <th class="upload-title" id="country">
                        <span>Country</span>
                    </th>
                    <!-- Country header end -->
                    <td class="pt-2">
                        <!-- multiselect dropdown for Country start -->
                        <ng-multiselect-dropdown name="Country" [placeholder]="''" formControlName="country"
                            [disabled]="disabled" [settings]="countryDropdownSettings" [data]="countryList"
                            (onSelect)="buttonDisable($event)" (onDeSelect)="buttonDisable($event)"
                            (onSelectAll)="buttonDisable($event)" (onDeSelectAll)="buttonDisable($event)">
                        </ng-multiselect-dropdown>
                        <!-- multiselect dropdown for Country end -->
                        <!---------------------------------->
                        <!-- validation for country start -->
                        <!---------------------------------->
                        <div
                            *ngIf="(form.get('country').touched || form.get('country').dirty) && form.get('country').invalid ">
                            <div *ngIf="form.get('country').errors['required']">
                                <span class="validation">Country is required</span>
                            </div>
                        </div>
                        <!---------------------------------->
                        <!-- validation for country end -->
                        <!---------------------------------->
                    </td>
                </tr>
                <!-- Country row end -->
                <!-- video json version row start -->
                <tr>
                    <!-- video json version header start -->
                    <th class="upload-title" id="jsonVer">
                        <span>Json Version</span>
                    </th>
                    <!-- video json version header end -->
                    <td class="pt-2">
                        <!-- multiselect dropdown for video json version start -->
                        <ng-multiselect-dropdown class="devicePageDeviceType" name="jsonVersion" [placeholder]="''"
                            formControlName="jsonVersion" [disabled]="disabled"
                            [settings]="dropdownSettingsForJsonVersion" [data]="jsonVersionList"
                            (onSelect)="buttonDisable($event)" (onDeSelect)="buttonDisable($event)"
                            (onSelectAll)="buttonDisable($event)" (onDeSelectAll)="buttonDisable($event)">
                        </ng-multiselect-dropdown>
                        <!-- multiselect dropdown for video json version end -->
                    </td>
                </tr>
                <!-- video json version row end -->
                <!-- partNumber row start -->
                <tr>
                    <!-- partNumber header start -->
                    <th class="upload-title" id="partNumber">
                        <span>Part Number</span>
                    </th>
                    <!-- partNumber header end -->
                    <td>
                        <!-- partNumber input start -->
                        <input type="text" class="upload-form-control mt-2" tabindex="-1" id="partNumber"
                            name="partNumber" formControlName="partNumber"
                            (keyup)="buttonDisable($any($event.target)?.value)" required>
                        <!-- partNumber input end -->
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td>
                        <!-- validation for partNumber start -->
                        <div
                            *ngIf="(form.get('partNumber').touched || form.get('partNumber').dirty) && form.get('partNumber').invalid ">
                            <div *ngIf="form.get('partNumber').errors['required']">
                                <span class="validation">Part Number is required</span>
                            </div>
                            <div *ngIf="form.get('partNumber').errors['maxlength']">
                                <span class="validation">{{small_textBoxMaxCharactersAllowedMessage}}</span>
                            </div>
                            <div *ngIf="form.get('partNumber').errors['pattern']">
                                <span class="validation">{{specialCharacterErrorMessage}}</span>
                            </div>
                        </div>
                        <!-- validation for partNumber end -->
                    </td>
                </tr>
                <!-- partNumber row end -->
                <!-- file selection start -->
                <tr>
                    <td colspan="2" style="padding-top: 10px;">
                        <input type="file" id="file" style="display: none;" name="file" accept=".json, .zip" required
                            (change)="handleFileInput($event)" multiple>
                        <input type="hidden" name="fileHidden" formControlName="fileSelector" />
                        <label class="btn btn-sm btn-orange Pointer" for="file">Import Files</label>
                        <small *ngIf="fileSelectedState" class="upload-success"
                            style="font-size: 12px;vertical-align: middle;">
                            2 files selected
                        </small>
                        <div *ngIf="fileValidation" class="validation">
                            select two files (zip, json)
                        </div>
                    </td>
                </tr>
                <!-- file selection end -->

                <!-- Loading Section Start -->
                <tr *ngIf="progressVisible">
                    <td colspan="2" class="upload-container">
                        <h3>Uploading File...</h3>

                        <!-- Progress Bar -->
                        <div class="progress-bar-wrapper">
                            <div class="progress-bar" [style.width.%]="uploadProgress"></div>
                        </div>

                        <!-- Progress Details -->
                        <div class="progress-details">
                            <p class="m-0">{{ uploadProgress.toFixed(2) }}% Uploaded</p>
                        </div>
                        <p class="text-info">*Please don't click outside to avoid interrupting the firmware upload.</p>
                    </td>
                </tr>
                <!-- Loading Section End -->

            </table>
            <!-- table end -->
        </form>
        <!-- upload form end -->
    </div>
</div>
<!-- model body end -->
<div class="modal-footer">
    <!-- cancel button -->
    <button type="button" class="btn btn-sm btn-outline-secondary" (click)="decline()" [disabled]="progressVisible">{{
        btnCancelText }}</button>
    <!-- accept button -->
    <button type="button" class="btn btn-sm btn-orange" id="uploadBtnSw" (click)="accept()" [disabled]="disableBtn">{{
        btnOkText }}</button>
</div>