import { HttpErrorResponse } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { throwError } from 'rxjs';
import { COUNTRY_ALREADY_EXISTING } from 'src/app/app.constants';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { CountryApiCallService } from 'src/app/shared/Service/CountryService/country-api-call.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { CreateCountryComponent } from './create-country.component';

describe('CreateCountryComponent', () => {
  let component: CreateCountryComponent;
  let fixture: ComponentFixture<CreateCountryComponent>;
  let countryApiCallServiceMock: jasmine.SpyObj<CountryApiCallService>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let exceptionHandlingService: ExceptionHandlingService;


  beforeEach(async () => {
    countryApiCallServiceMock = jasmine.createSpyObj('CountryApiCallService', ['createCountry']);
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);


    await TestBed.configureTestingModule({
      declarations: [CreateCountryComponent],
      imports: [ReactiveFormsModule,
        FormsModule,
        NgMultiSelectDropDownModule.forRoot()],
      providers: [
        NgbActiveModal,
        PermissionService,
        LocalStorageService,
        AuthJwtService,
        SessionStorageService,
        CommonsService,
        ConfirmDialogService,
        { provide: CountryApiCallService, useValue: countryApiCallServiceMock },
        commonsProviders(toastrServiceMock)
      ]
    }).compileComponents();
    fixture = TestBed.createComponent(CreateCountryComponent);
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('create country error scenario - handles generic server error', () => {
    // Spy on the customErrorMessage method in exceptionHandlingService to verify error handling
    spyOn(exceptionHandlingService, 'customErrorMessage');

    // Simulate an HTTP error response with a 500 status code
    const httpErrorResponse = new HttpErrorResponse({ status: 500, statusText: 'Internal Server Error' });

    // Mock the createCountry method to throw the simulated HTTP error
    countryApiCallServiceMock.createCountry?.and.returnValue(throwError(() => httpErrorResponse));

    // Trigger the addCountry method in the component
    component.addCountry();

    // Verify that the customErrorMessage method was called with the error response
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalledWith(httpErrorResponse);
  });

  it('create country error scenario - handles country already existing error', () => {
    // Simulate an HTTP error response with a 409 status code
    const httpErrorResponse = new HttpErrorResponse({ status: 409 });

    // Mock the createCountry method to throw the simulated HTTP error
    countryApiCallServiceMock.createCountry?.and.returnValue(throwError(() => httpErrorResponse));

    // Trigger the addCountry method in the component
    component.addCountry();

    // Verify that the toastrService displays the expected error message for a 409 conflict
    expect(toastrServiceMock.error).toHaveBeenCalledWith(COUNTRY_ALREADY_EXISTING);
  });

});
