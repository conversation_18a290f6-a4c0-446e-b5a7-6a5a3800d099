import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { CountryRequestBody } from 'src/app/model/Country/CountryRequestBody.model';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { CountryCacheService } from 'src/app/shared/Service/CacheService/countrycache.service';
import { CountryService } from 'src/app/shared/Service/CountryService/country.service';
import { RoleApiCallService } from 'src/app/shared/Service/RoleService/role-api-call.service';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { CountryFilterComponent } from './country-filter.component';
import { EnumMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';

describe('CountryFilterComponent', () => {
  let component: CountryFilterComponent;
  let fixture: ComponentFixture<CountryFilterComponent>;
  let countryService: CountryService;

  beforeEach(async () => {
    const toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    const localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);

    // Mock the return value of retrieve for the getPermissionObject function
    localStorageServiceMock.retrieve.and.returnValue('mockedPermissionObject');
    const confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['open', 'close']);

    await TestBed.configureTestingModule({
      declarations: [CountryFilterComponent, PrintListPipe],
      imports: [ReactiveFormsModule, NgMultiSelectDropDownModule.forRoot()],
      providers: [
        CommonOperationsService,
        RoleApiCallService,
        CommonsService,
        CountryCacheService,
        PrintListPipe,
        EnumMappingDisplayNamePipe,
        ExceptionHandlingService,
        AuthJwtService,
        SessionStorageService,
        HidePermissionNamePipe,
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(CountryFilterComponent);
    countryService = TestBed.inject(CountryService);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set form as invalid in onInitSubject', fakeAsync(() => {
    // Simulate form values before onInit
    component.countryRequestBody = new CountryRequestBody("United States", "English");
    component.isFilterComponentInitWithApicall = false;

    // Trigger Angular's initial change detection
    fixture.detectChanges();

    // Now set invalid form values to trigger validation
    component.filterCountryForm.get('countryName').setValue('United Statesssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssss');
    component.filterCountryForm.get('languageName').setValue("In'valid@@Language");  // Invalid due to pattern

    // Mark all controls as touched and dirty to simulate user interaction and trigger validation
    component.filterCountryForm.markAllAsTouched();
    component.filterCountryForm.markAsDirty();

    // Force validation to update
    component.filterCountryForm.updateValueAndValidity();  // This ensures validators run again

    // Simulate Angular's change detection to reflect the new state
    fixture.detectChanges();

    // Now simulate the subject emission (as done in your onInitSubject method)
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    countryService.getCountryRefreshSubject().next(listingPageReloadSubjectParameter);
    tick();  // Simulate the passage of time

    // Verify that the form controls were cleared if the form was invalid
    expect(component.filterCountryForm.get('countryName').value).toEqual(null);
    expect(component.filterCountryForm.get('languageName').value).toEqual(null);
  }));

});
