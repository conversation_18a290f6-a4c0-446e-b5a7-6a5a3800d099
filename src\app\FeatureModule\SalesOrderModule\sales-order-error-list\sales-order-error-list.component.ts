import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ChangeDetectorRef, Component, EventEmitter, Output } from '@angular/core';
import { isUndefined } from 'is-what';
import { Subscription } from 'rxjs';
import { DateTimeDisplayFormat, ITEMS_PER_PAGE } from 'src/app/app.constants';
import { SalesOrderFaieldSearchRequestBody } from 'src/app/model/SalesOrder/SalesOrderFaieldSearchRequestBody.model';
import { SalesOrderFailedFilterAction } from 'src/app/model/SalesOrder/SalesOrderFailedFilterAction.model';
import { SalesOrderFailedPageResponse } from 'src/app/model/SalesOrder/SalesOrderFailedPageResponse.model';
import { SalesOrderFailedResponse } from 'src/app/model/SalesOrder/SalesOrderFailedResponse.model';
import { SalesOrderSchedulerManualSyncTimeResponse } from 'src/app/model/SalesOrder/SalesOrderSchedulerManuleSyncTimeResponse.model';
import { SalesOrderSchedulerSyncTimeResponse } from 'src/app/model/SalesOrder/SalesOrderSchedulerSyncTimeResponse.model';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { SalesOrderApiCallService } from 'src/app/shared/Service/SalesOrderService/sales-order-api-call.service';
import { SalesOrderService } from 'src/app/shared/Service/SalesOrderService/sales-order.service';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { SalesOrderOperationsEnum } from 'src/app/shared/enum/Operations/SalesOrderOperations.enum';
import { collapseFilterTextEnum } from 'src/app/shared/enum/collapseFilterButtonText.enum';
import { ManualSyncService } from 'src/app/shared/manual-sync.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';

@Component({
  selector: 'app-sales-order-error-list',
  templateUrl: './sales-order-error-list.component.html',
  styleUrls: ['./sales-order-error-list.component.css']
})
export class SalesOrderErrorListComponent {

  @Output('hideShowSyncAndErrorListing') hideShowSyncAndErrorListing = new EventEmitter();

  loading: boolean = false;

  //Page
  itemsPerPage: number = 0;
  page: number = 0;
  previousPage: number = 0;
  totalItems: number = 0;

  //Totel Count Display
  totalRecordDisplay: number = 0;
  totalRecord: number = 0;

  //Page Size DropDown
  dataSizes: string[] = [];
  drpselectsize: number = ITEMS_PER_PAGE;
  dateTimeDisplayFormat: string = DateTimeDisplayFormat;

  //Filter
  isFilterComponentInitWithApicall: boolean = true;
  isFilterHidden: boolean = true;
  hideShowFilterButtonText: string = collapseFilterTextEnum.HIDE_FILTER;

  //Sales Order List
  salesOrderFailedResponse: SalesOrderFailedResponse[] = [];

  failedSalesOrderOperations: string[] = [];

  //subscription
  subscriptionForLoading: Subscription;
  subscriptionForSalesOrderFailedListFilterRequestParameter: Subscription;

  //Sales Order Failed serach request body store
  salesOrderFaieldSearchRequestBody: SalesOrderFaieldSearchRequestBody = null;

  //checkboxHide
  showCheckBox: boolean = false;

  //Sync Time 
  salesOrderSchedulerSyncTimeResponse: SalesOrderSchedulerSyncTimeResponse = null;

  constructor(
    private authservice: AuthJwtService,
    private commonsService: CommonsService,
    private salesOrderService: SalesOrderService,
    private salesOrderApiCallService: SalesOrderApiCallService,
    private exceptionService: ExceptionHandlingService,
    private manualSyncService: ManualSyncService,
    private commonOperationsService: CommonOperationsService,
    private cdr: ChangeDetectorRef
  ) { }

  /**
   * <AUTHOR>
   */
  public ngOnInit(): void {
    if (!this.authservice.isAuthenticate()) {
      this.authservice.loginNavigate();
    } else {
      this.page = 0;
      this.dataSizes = this.commonsService.accessDataSizes();
      this.failedSalesOrderOperations = this.commonOperationsService.failedSalesOrderOperations();
      this.isFilterComponentInitWithApicall = true;
      this.isFilterHidden = false;
      this.itemsPerPage = ITEMS_PER_PAGE;
      this.drpselectsize = ITEMS_PER_PAGE;
      this.getSalesOrderSchedulerSyncTime();
    }
    this.subjectInit();
  }

  private subjectInit(): void {
    /**
     * This Subject call from Filter component
     * Load all the Data
     * <AUTHOR>
     */
    this.subscriptionForSalesOrderFailedListFilterRequestParameter = this.salesOrderService.getSalesOrderFailedListFilterRequestParameterSubject()?.subscribe((salesOrderFailedRequestParameter: SalesOrderFailedFilterAction) => {
      if (salesOrderFailedRequestParameter.listingPageReloadSubjectParameter.isReloadData) {
        if (salesOrderFailedRequestParameter.listingPageReloadSubjectParameter.isDefaultPageNumber) {
          this.resetPage()
        }
        this.loadAll(salesOrderFailedRequestParameter.salesOrderFaieldSearchRequestBody);
      }
    });
  }

  /**
   * Destroy subscription
   * <AUTHOR>
   */
  public ngOnDestroy(): void {
    if (!isUndefined(this.subscriptionForSalesOrderFailedListFilterRequestParameter)) { this.subscriptionForSalesOrderFailedListFilterRequestParameter.unsubscribe() }
  }

  /**
   * Reset Page
   * <AUTHOR>
   */
  private resetPage(): void {
    this.page = 0;
    this.previousPage = 1;
  }

  /**
   * Clear all filter ,Reset Page and Reload the page
   * <AUTHOR>
   */
  public refreshFilter(): void {
    this.resetPage();
    this.getSalesOrderSchedulerSyncTime();
    this.filterPageSubjectCallForReloadPage(true, true);
  }

  /**
   * Item par page Value Changes like (10,50,100)
   * <AUTHOR>
   * @param datasize 
   */
  public changeDataSize(datasize): void {
    this.setLoadingStatus(true);
    this.itemsPerPage = datasize.target.value;
    this.filterPageSubjectCallForReloadPage(true, false);
  }


  /**
   * Change The Page
   * callSalesOrderListRefreshSubject ->Call the filter component
   * filter not clear and send with filter requrest and load data 
   * <AUTHOR>
   * @param page 
   */
  public loadPage(page): void {
    if (page !== this.previousPage) {
      this.previousPage = page;
      this.filterPageSubjectCallForReloadPage(false, false);
    }
  }

  /**
   * Call Filter component subject and reload page
   * <AUTHOR>
   * @param isDefaultPageNumber 
   * @param isClearFilter 
   */
  public filterPageSubjectCallForReloadPage(isDefaultPageNumber: boolean, isClearFilter: boolean): void {
    let listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, isDefaultPageNumber, isClearFilter, false)
    this.salesOrderService.callRefreshPageSubjectForFaildOrder(listingPageReloadSubjectParameter, this.isFilterHidden);
  }

  /**
   * Toggle Filter
   * <AUTHOR>
   * @param id 
   */
  public toggleFilter(): void {
    this.isFilterComponentInitWithApicall = false;
    this.isFilterHidden = !this.isFilterHidden;
    if (this.isFilterHidden) {
      this.hideShowFilterButtonText = collapseFilterTextEnum.SHOW_FILTER;
    } else {
      this.hideShowFilterButtonText = collapseFilterTextEnum.HIDE_FILTER;
    }
  }

  /**
   * Get Sales Order Faild List
   * 
   * <AUTHOR>
   * 
   * @param salesOrderFaieldSearchRequestBody 
   */
  public loadAll(salesOrderFaieldSearchRequestBody: SalesOrderFaieldSearchRequestBody): void {
    this.salesOrderFaieldSearchRequestBody = salesOrderFaieldSearchRequestBody;
    let pageObj = {
      page: this.page - 1,
      size: this.itemsPerPage
    }
    this.setLoadingStatus(true);
    this.salesOrderApiCallService.getSalesOrderFailedList(salesOrderFaieldSearchRequestBody, pageObj)
      ?.subscribe(
        {
          next: (salesOrderFailedPageResponse: HttpResponse<SalesOrderFailedPageResponse>) => {
            if (salesOrderFailedPageResponse.status == 200) {
              this.paginateDataset(salesOrderFailedPageResponse.body);
            } else {
              this.salesOrderFailedResponse = [];
              this.totalRecordDisplay = 0;
              this.totalRecord = 0;
            }
            this.setLoadingStatus(false);
          },
          error: (error: HttpErrorResponse) => {
            this.setLoadingStatus(false);
            this.exceptionService.customErrorMessage(error);
          }
        }
      );
  }

  /**
   * salesOrder Failed Response set 
   * <AUTHOR>
   * @param rolePageResponse 
   */
  private paginateDataset(salesOrderFailedPageResponse: SalesOrderFailedPageResponse): void {
    this.totalItems = salesOrderFailedPageResponse.totalElements;
    this.salesOrderFailedResponse = salesOrderFailedPageResponse.content;
    this.page = salesOrderFailedPageResponse.number + 1;
    this.totalRecord = salesOrderFailedPageResponse.totalElements;
    this.totalRecordDisplay = salesOrderFailedPageResponse.numberOfElements;
    this.setLoadingStatus(false);
  }

  /**
   * Loading Status 
   * <AUTHOR>
   */
  private setLoadingStatus(status: boolean): void {
    this.loading = status;
    this.cdr.detectChanges();
  }

  /**
   * Hide/Show sync and error page
   * 
   * <AUTHOR>
   * @param isBooked 
   */
  public hideShowSyncAndErrorListingEmit(isBooked: boolean): void {
    this.hideShowSyncAndErrorListing.emit(isBooked);
  }


  /**
   * Get SalesOrder Scheduler Sync
   * 
   * <AUTHOR>
   */
  private async getSalesOrderSchedulerSyncTime(): Promise<void> {
    this.setLoadingStatus(true);
    this.salesOrderSchedulerSyncTimeResponse = await this.salesOrderApiCallService.getSalesOrderSchedulerSyncTime();
  }

  /**
  * Fail Sales Order operations
  * @param event 
  */
  public changeSalesOrderOperation(event): void {
    if (event.target.value === SalesOrderOperationsEnum.MANUAL_SYNC) {
      this.manualSync();
    }
    let selection = document.getElementById('failedSalesOrderOperation') as HTMLSelectElement;
    selection.value = SalesOrderOperationsEnum.SALES_ORDER_OPERATIONS;
  }

  /**
  * Initiates a manual synchronization process for sales orders.
  * 
  * <AUTHOR>
  * @returns {void}
  */
  public manualSync(): void {
    this.setLoadingStatus(true);
    this.salesOrderApiCallService.salesOrderManualSync().subscribe({
      next: (response: HttpResponse<SalesOrderSchedulerManualSyncTimeResponse>) => {
        if (response.body?.id) {
          this.setLoadingStatus(false);
          this.manualSyncService.confirm('Manual Sync', response.body)
            .then((confirmed: boolean) => {
              if (confirmed) {
                this.setLoadingStatus(true);
                this.getSalesOrderSchedulerSyncTime();
                this.filterPageSubjectCallForReloadPage(true, false);
              }
            });
        }
      },
      error: (error) => {
        this.exceptionService.customErrorMessage(error);
        this.setLoadingStatus(false);
      }
    });

  }
}
