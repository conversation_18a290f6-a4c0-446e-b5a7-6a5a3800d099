import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DeleteInventoryDialogComponent } from './delete-inventory-dialog.component';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

describe('DeleteInventoryDialogComponent', () => {
  let component: DeleteInventoryDialogComponent;
  let fixture: ComponentFixture<DeleteInventoryDialogComponent>;
  let mockActiveModal: jasmine.SpyObj<NgbActiveModal>;

  beforeEach(async () => {
    // Create a mock for NgbActiveModal
    mockActiveModal = jasmine.createSpyObj('NgbActiveModal', ['close', 'dismiss']);

    await TestBed.configureTestingModule({
      declarations: [DeleteInventoryDialogComponent],
      providers: [NgbActiveModal, { provide: NgbActiveModal, useValue: mockActiveModal }]
    })
      .compileComponents();

    fixture = TestBed.createComponent(DeleteInventoryDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });


  it('should call decline and close the modal with false', () => {
    // Act
    component.decline();

    // Assert
    expect(mockActiveModal.close).toHaveBeenCalledWith(false);
  });

  it('should call dismiss and dismiss the modal', () => {
    // Act
    component.dismiss();

    // Assert
    expect(mockActiveModal.dismiss).toHaveBeenCalled();
  });
});
