import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { NgbPagination } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { INTERNAL_SERVER_ERROR, ITEMS_PER_PAGE } from 'src/app/app.constants';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { KitManagementDetailResponse } from 'src/app/model/KitManagement/KitManagementDetailResponse.model';
import { KitManagementPageResponse } from 'src/app/model/KitManagement/KitManagementPageResponse.model';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { KitPartnumberDisplayPipe } from 'src/app/shared/pipes/Kit/kitPartnumberDisplay.pipe';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { CountryCacheService } from 'src/app/shared/Service/CacheService/countrycache.service';
import { CountryAndLanguageService } from 'src/app/shared/Service/CountryAndLanguageService/country-and-language.service';
import { KitManagemantApiCallService } from 'src/app/shared/Service/KitManagemant/kit-managemant-api-call.service';
import { KitManagemantService } from 'src/app/shared/Service/KitManagemant/kit-managemant.service';
import { commonsProviders, countryListResponse, getLanguageListResponse, operationsListTestcase, testAuthentication, testDropdownInteraction, testPagination, testToggleFilter } from 'src/app/Tesing-Helper/test-utils';
import { KitManagementDetailComponent } from '../kit-management-detail/kit-management-detail.component';
import { KitManagementFilterComponent } from '../kit-management-filter/kit-management-filter.component';
import { KitManagementListComponent } from './kit-management-list.component';
import { EnumMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';

describe('KitManagementListComponent', () => {
  let component: KitManagementListComponent;
  let fixture: ComponentFixture<KitManagementListComponent>;
  let authServiceSpy: jasmine.SpyObj<AuthJwtService>;
  let permissionServiceSpy: jasmine.SpyObj<PermissionService>;
  let countryCacheServiceSpy: jasmine.SpyObj<CountryCacheService>;
  let countryAndLanguageServiceSpy: jasmine.SpyObj<CountryAndLanguageService>;
  let kitManagemantApiCallServiceSpy: jasmine.SpyObj<KitManagemantApiCallService>;
  let exceptionHandlingService: ExceptionHandlingService;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let kitManagementServiceSpy: KitManagemantService;

  let bridgeKitListResponse: KitManagementPageResponse = {
    "content": [{
      "id": 5126,
      "description": null,
      "kitPartNumber": "kit_dummy_part_number_5",
      "country": "Ireland",
      "language": "English",
      "videoVersion": "2.2",
      "softWareVersion": "********",
      "modifiedDate": 1729601832010,
      "softWarePartNumber": "P007214-0148",
      "bridgePartNumber": "P006030-179",
      "probePartNumbers": [],
      "dummyKitPartNumber": true
    }, {
      "id": 5127,
      "description": null,
      "kitPartNumber": "kit_dummy_part_number_6",
      "country": "United States",
      "language": "English",
      "videoVersion": "2.2",
      "softWareVersion": "********",
      "modifiedDate": 1729601832010,
      "softWarePartNumber": "P007214-0149",
      "bridgePartNumber": "P006030-179",
      "probePartNumbers": [],
      "dummyKitPartNumber": true
    }, {
      "id": 5128,
      "description": null,
      "kitPartNumber": "kit_dummy_part_number_7",
      "country": "Service",
      "language": "English",
      "videoVersion": "2.3",
      "softWareVersion": "*******",
      "modifiedDate": 1729601832010,
      "softWarePartNumber": "P007214-0150",
      "bridgePartNumber": "P006030-201",
      "probePartNumbers": [],
      "dummyKitPartNumber": true
    }, {
      "id": 5129,
      "description": "Kosmos Bridge Tablet",
      "kitPartNumber": "P006909-014",
      "country": "Croatia",
      "language": "Portuguese",
      "videoVersion": "2.2",
      "softWareVersion": "********",
      "modifiedDate": 1729601832010,
      "softWarePartNumber": "P007214-0145",
      "bridgePartNumber": "P006030-179",
      "probePartNumbers": ["P006709-001", "P006025-002"],
      "dummyKitPartNumber": false
    }, {
      "id": 5130,
      "description": "hina é o berço do chá, e sua cultura de chá é rica",
      "kitPartNumber": "kit_dummy_part_number_4",
      "country": "India",
      "language": "English",
      "videoVersion": "2.2",
      "softWareVersion": "*******",
      "modifiedDate": 1729601832010,
      "softWarePartNumber": "P007214-0147",
      "bridgePartNumber": "P006030-179",
      "probePartNumbers": [],
      "dummyKitPartNumber": true
    }, {
      "id": 5131,
      "description": "中国是茶的故乡，茶文化源远流长。茶在中国不仅是一种饮品，更是一种生活方式。无论是日常的饮用，还是在重要的社交场合，茶都扮演着重要的角色。",
      "kitPartNumber": "kit_dummy_part_number_3",
      "country": "Japan",
      "language": "English",
      "videoVersion": "2.2",
      "softWareVersion": "*******",
      "modifiedDate": 1729601832010,
      "softWarePartNumber": "P007214-0146",
      "bridgePartNumber": "P006030-179",
      "probePartNumbers": [],
      "dummyKitPartNumber": true
    }],
    "pageable": {
      "pageNumber": 0,
      "pageSize": 10,
      "sort": {
        "empty": true,
        "sorted": false,
        "unsorted": true
      },
      "offset": 0,
      "paged": true,
      "unpaged": false
    },
    "last": true,
    "totalPages": 1,
    "totalElements": 6,
    "size": 10,
    "number": 0,
    "sort": {
      "empty": true,
      "sorted": false,
      "unsorted": true
    },
    "first": true,
    "numberOfElements": 6,
    "empty": false
  }
  let kitDetailResponse: KitManagementDetailResponse = {
    "id": 5126,
    "description": null,
    "kitPartNumber": "kit_dummy_part_number_5",
    "country": "Ireland",
    "language": "English",
    "videoVersion": "2.2",
    "softWareVersion": "********",
    "modifiedDate": 1729601832010,
    "softWarePartNumber": "P007214-0148",
    "bridgePartNumber": "P006030-179",
    "probePartNumbers": [],
    "dummyKitPartNumber": true
  }


  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    const confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['open', 'close']);
    const localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);
    authServiceSpy = jasmine.createSpyObj('AuthJwtService', ['isAuthenticate', 'loginNavigate']);
    permissionServiceSpy = jasmine.createSpyObj('PermissionService', ['getKitManagementPermission', 'getLanguagePermission',]);
    kitManagemantApiCallServiceSpy = jasmine.createSpyObj('KitManagemantApiCallService', ['getKitList', 'getKitRevVersion', 'getKitDetail']);
    exceptionHandlingService = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);
    countryAndLanguageServiceSpy = jasmine.createSpyObj('CountryAndLanguageServiceSpy', ['getLanguageList']);
    countryCacheServiceSpy = jasmine.createSpyObj('CountryCacheServiceSpy', ['getCountryListFromCache']);

    // Mock the return value of retrieve for the getPermissionObject function
    localStorageServiceMock.retrieve?.and.returnValue('mockedPermissionObject');
    await TestBed.configureTestingModule({
      declarations: [KitManagementListComponent, KitManagementFilterComponent, KitManagementDetailComponent, KitPartnumberDisplayPipe],
      imports: [NgbPagination, ReactiveFormsModule, FormsModule, NgMultiSelectDropDownModule.forRoot()],
      providers: [
        SessionStorageService,
        HidePermissionNamePipe,
        EnumMappingDisplayNamePipe,
        PrintListPipe,
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        { provide: AuthJwtService, useValue: authServiceSpy },
        { provide: PermissionService, useValue: permissionServiceSpy },
        { provide: CountryCacheService, useValue: countryCacheServiceSpy },
        { provide: CountryAndLanguageService, useValue: countryAndLanguageServiceSpy },
        { provide: KitManagemantApiCallService, useValue: kitManagemantApiCallServiceSpy },
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(KitManagementListComponent);
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService);
    kitManagementServiceSpy = TestBed.inject(KitManagemantService);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {

    it('should initialize the component when the user is authenticated', async () => {

      authServiceSpy.isAuthenticate.and.returnValue(true);
      permissionServiceSpy.getKitManagementPermission?.and.returnValue(true);
      // Initialize component and trigger change detection
      component.ngOnInit();
      fixture.detectChanges();

      spyOn(component, 'clickOnRefreshButton')?.and.callThrough();
      const button = fixture.nativeElement.querySelector('#refresh_bridgeKitList');
      button?.click();
      expect(component.clickOnRefreshButton).toHaveBeenCalled();

      testAuthentication(authServiceSpy, component, fixture);

      //operations List
      operationsListTestcase(fixture, '#bridgeKitOperation', 1)

      // Test dropdown interaction
      testDropdownInteraction(fixture, component, '#kitManagemantListShowEntry');

      // Test pagination
      testPagination(fixture, component, '#bridgeKit-pagination', 2);

    });

    it('should initialize component if authenticated', fakeAsync(() => {
      // **Arrange: Mock authentication and API response**
      // Simulate an authenticated user
      authServiceSpy.isAuthenticate?.and.returnValue(true);

      // Mock API call for kit revision version details
      kitManagemantApiCallServiceSpy.getKitRevVersion?.and.returnValue(Promise.resolve('1.0.0'));

      // **Act: Initialize the component and trigger async behavior**
      // Trigger `ngOnInit` lifecycle method and wait for promises to resolve
      component.ngOnInit();
      tick();

      // **Assert: Verify component state after initialization**
      expect(component.page).toBe(0); // Ensure page is set to the default value of 0
      expect(component.dataSizes.length).toBeGreaterThan(0); // Ensure dataSizes array has items
      expect(component.kitRevVersionResponse).toEqual('1.0.0'); // Ensure API response is correctly assigned
    }));

    // Test: Toggles filter visibility and updates the button text
    it('should toggle filter visibility and update the button text', () => {
      // Use the generic utility to test the toggle behavior
      testToggleFilter(component);
    });

    it('should call toastrService.error with INTERNAL_SERVER_ERROR', () => {
      // **Arrange: Set up authentication mock and simulate an error response for getKitList**
      authServiceSpy.isAuthenticate?.and.returnValue(true);
      const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });

      // Mock API call to simulate error response
      kitManagemantApiCallServiceSpy.getKitList?.and.returnValue(throwError(() => mockError));

      // Spy on exceptionHandlingService's error handling method
      spyOn(exceptionHandlingService, 'customErrorMessage')?.and.callThrough();

      // **Act: Trigger the component's initialization to invoke getKitList and error handling**
      component.loadAll(null);

      // **Assert: Ensure error handling method was called**
      expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled(); // Error handling should be triggered

      // **Assert: Ensure toastrService displays the INTERNAL_SERVER_ERROR message**
      expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR); // Error message should be displayed
    });

    it('LoadAll method gives error', () => {
      // **Arrange: Set up authentication mock and simulate an error response from getKitList service**
      authServiceSpy.isAuthenticate?.and.returnValue(true);

      // Mock API response with an error status code
      kitManagemantApiCallServiceSpy.getKitList?.and.returnValue(of(new HttpResponse<KitManagementPageResponse>({
        body: null,
        status: 500, // Simulate server error status code
        statusText: 'OK',
      })));

      // **Act: Initialize component to trigger loadAll method which calls getKitList**
      component.loadAll(null);

      // **Assert: Verify no data is loaded when an error response is received**
      expect(component.kitResponseList.length).toEqual(0); // No data should be loaded into kitResponseList
      expect(component.totalRecordDisplay).toEqual(0); // Total record display count should be 0
      expect(component.totalRecord).toEqual(0); // Total records should be 0
      expect(component.loading).toBeFalse(); // Ensure loading state is false after the process completes
    });
  });

  it('should update itemsPerPage when a new option is selected in the dropdown', () => {
    // **Arrange: Set up the mock authentication and initialize the component**
    authServiceSpy.isAuthenticate?.and.returnValue(true);
    component.ngOnInit();
    fixture.detectChanges(); // Ensure view updates after ngOnInit

    // **Act: Select a new value from the dropdown and dispatch a change event**
    const selectElement = fixture.nativeElement.querySelector('#kitManagemantListShowEntry');
    selectElement.value = selectElement?.options[3].value; // Select the 4th option (index 3)
    selectElement.dispatchEvent(new Event('change')); // Simulate the change event triggered by the user

    // **Assert: Verify that the itemsPerPage value has been updated**
    // Ensure that the component reflects the newly selected option
    expect(component.itemsPerPage).toBe(selectElement?.options[3]?.value);
  });

  it('should correctly update the page number when page change occurs', () => {
    // **Arrange: Set up the mock authentication and initialize the component**
    authServiceSpy.isAuthenticate?.and.returnValue(true);
    component.ngOnInit();
    fixture.detectChanges(); // Ensure the view is updated after component initialization

    // **Spy on the loadPage method to verify it's called correctly on page change**
    spyOn(component, 'loadPage')?.and.callThrough();

    // **Act: Simulate a page change event (changing to page 2)**
    const paginationElement = fixture.debugElement.query(By.css('#bridgeKit-pagination'));
    paginationElement.triggerEventHandler('pageChange', 2); // Trigger page change to page 2

    fixture.detectChanges(); // Update the view with the page change

    // **Assert: Verify that the page number has been updated and loadPage is called**
    expect(component.page).toBe(2); // Check that the page number is updated
    expect(component.loadPage).toHaveBeenCalledWith(2); // Ensure loadPage method is called with page number 2
  });


  it('should initialize form controls on ngOnInit and set initial values', async () => {
    // **Arrange: Mock necessary services and prepare the component state**
    authServiceSpy.isAuthenticate?.and.returnValue(true);
    kitManagemantApiCallServiceSpy.getKitRevVersion?.and.returnValue(Promise.resolve('1.0.0'));
    permissionServiceSpy.getKitManagementPermission?.and.returnValue(true);
    kitManagemantApiCallServiceSpy.getKitList?.and.returnValue(of(new HttpResponse<KitManagementPageResponse>({
      body: bridgeKitListResponse,
      status: 200,
      statusText: 'OK',
    })));

    spyOn(kitManagementServiceSpy, 'callKitListFilterRequestParameterSubject')?.and.callThrough();
    spyOn(component, 'loadAll')?.and.callThrough();

    // **Act: Trigger initial change detection and ngOnInit**
    fixture.detectChanges(); // Ensure initial change detection
    component.ngOnInit(); // Initialize the component
    fixture.detectChanges(); // Ensure the view updates after ngOnInit

    const filterComponent = fixture.debugElement.query(By.directive(KitManagementFilterComponent)).componentInstance;

    // **Assert: Verify the form controls and component state after initialization**
    expect(filterComponent).toBeTruthy(); // Ensure the filter component is present

    // Verify the initial form control values
    expect(filterComponent.filterKitForm?.get('kitPartNumber').value).toBeNull();
    expect(filterComponent.filterKitForm?.get('bridgePartNumber').value).toBeNull();
    expect(filterComponent.filterKitForm?.get('countries').value).toEqual([]);
    expect(filterComponent.filterKitForm?.get('softwareLanguage').value).toEqual([]);
    expect(filterComponent.filterKitForm?.get('videoVersion').value).toBeNull();
    expect(filterComponent.filterKitForm?.get('buildVersion').value).toBeNull();

    // Verify other component properties after initialization
    expect(component.otsKitDisplayPermissions).toBeTrue();
    expect(component.itemsPerPage).toBe(ITEMS_PER_PAGE);
    expect(component.drpselectsize).toBe(ITEMS_PER_PAGE);
    expect(component.previousPage).toBe(1);
    expect(component.dataSizes).toEqual(['10', '25', '50', '100']);

    // Assert that the filter request has been triggered
    expect(kitManagementServiceSpy.callKitListFilterRequestParameterSubject).toHaveBeenCalled();

    // Verify the data initialization for the component
    expect(component.totalItems).toEqual(bridgeKitListResponse.totalElements);
    expect(component.kitResponseList).toEqual(bridgeKitListResponse.content);
    expect(component.totalRecord).toEqual(bridgeKitListResponse.totalElements);
    expect(component.totalRecordDisplay).toEqual(bridgeKitListResponse.numberOfElements);
  });

  it('should call selectAllItem method when the "Select All" checkbox is clicked', async () => {
    // **Arrange: Set up mocks and initial component state**
    authServiceSpy.isAuthenticate?.and.returnValue(true);
    permissionServiceSpy.getKitManagementPermission?.and.returnValue(true);
    kitManagemantApiCallServiceSpy.getKitList?.and.returnValue(of(new HttpResponse<KitManagementPageResponse>({
      body: bridgeKitListResponse,
      status: 200,
      statusText: 'OK',
    })));
    spyOn(component, 'loadAll')?.and.callThrough();
    spyOn(component, 'selectAllItem')?.and.callThrough();

    component.isCheckBoxDiaply = true;
    component.selectAllCheckboxId = 'selectAllkit';
    component.ngOnInit();
    fixture.detectChanges();

    await fixture.whenStable();

    // **Act: Simulate a click on the "Select All" checkbox**
    const selectElementDetails = fixture.nativeElement.querySelector('#selectAllkit');
    selectElementDetails?.click();

    // **Assert: Verify that selectAllItem is called and totalRecord is updated**
    expect(component.selectAllItem).toHaveBeenCalledWith(true); // Validate the method call
    expect(component.totalRecord).toEqual(component.selectedKitIdList.length); // Ensure the correct record count
  });

  it('should toggle checkbox selection when one checkbox is clicked', async () => {
    // **Arrange: Set up mocks for kit data and services**
    authServiceSpy.isAuthenticate?.and.returnValue(true);
    permissionServiceSpy.getKitManagementPermission?.and.returnValue(true);
    permissionServiceSpy.getLanguagePermission?.and.returnValue(true);
    kitManagemantApiCallServiceSpy.getKitList?.and.returnValue(of(new HttpResponse<KitManagementPageResponse>({
      body: bridgeKitListResponse,
      status: 200,
      statusText: 'OK',
    })));
    kitManagemantApiCallServiceSpy.getKitDetail?.and.returnValue(of(new HttpResponse<KitManagementDetailResponse>({
      body: kitDetailResponse,
      status: 200,
      statusText: 'OK',
    })));

    countryAndLanguageServiceSpy.getLanguageList?.and.returnValue(Promise.resolve(getLanguageListResponse));
    countryCacheServiceSpy.getCountryListFromCache?.and.returnValue(Promise.resolve(countryListResponse));

    spyOn(component, 'loadAll')?.and.callThrough();
    spyOn(component, 'selectCheckbox')?.and.callThrough();

    component.chkPreFix = 'kit';
    component.isCheckBoxDiaply = true;
    component.ngOnInit();
    fixture.detectChanges();

    await fixture.whenStable();

    // **Act: Simulate a click on a single checkbox**
    const selectElement = fixture.nativeElement.querySelector('#kit5126kit');
    selectElement?.click(); // First click to select

    // **Assert: Ensure the checkbox selection is reflected in the selectedKitIdList**
    expect(component.selectCheckbox).toHaveBeenCalled(); // Ensure the selectCheckbox method was called
    expect(component.selectedKitIdList.length).toEqual(1); // One item should be selected

    // **Act Again: Simulate a second click to unselect**
    selectElement?.click(); // Second click to unselect

    // **Assert: Ensure the item is deselected**
    expect(component.selectedKitIdList.length).toEqual(0); // List should be empty after unselect

    // **Verify that kit filter component and search filter button work as expected**
    const kitfilterDebugElement = fixture.debugElement.query(By.css('app-kit-management-filter'));
    const kitfilterComponent = kitfilterDebugElement.componentInstance;
    expect(kitfilterComponent).toBeTruthy();

    const searchFilterButton = fixture.nativeElement.querySelector('#bridgeKitFilterSearch');
    searchFilterButton?.click();
    await fixture.whenStable();
    fixture.detectChanges();

    // Assert: Confirm toastrService.error was called with appropriate message
    expect(toastrServiceMock.info).toHaveBeenCalledWith("Please Select Filter To Search");

    // **Navigate to the kit detail view and validate the interaction**
    const selectElementDetails = fixture.nativeElement.querySelector('#bridgeKitListToDeatil');
    selectElementDetails.click();

    expect(component.bridgeWorldListDisplay).toBeFalsy();
    expect(component.bridgeWorldDetailDisplay).toBeTruthy();

    fixture.detectChanges();  // Ensure template updates
    await fixture.whenStable(); // Wait for async tasks

    const kitDetailDebugElement = fixture.debugElement.query(By.css('app-kit-management-detail'));
    const kitDetailComponent = kitDetailDebugElement.componentInstance;

    // Check if kit details are passed correctly
    expect(kitDetailComponent.kitManagementDetailResponse).toEqual(kitDetailResponse);
    expect(kitDetailDebugElement).toBeTruthy();

    // Verify that refresh button in detail view triggers refresh action
    spyOn(kitDetailComponent, 'refreshBridgekitDetailPage').and.callThrough();
    const bridgeDetailRefreshButton = fixture.nativeElement.querySelector('#bridgeDetailRefresh');
    bridgeDetailRefreshButton.click();
    expect(kitDetailComponent.refreshBridgekitDetailPage).toHaveBeenCalled();

    // Simulate the back button functionality to return to the list
    component.isFilterHidden = true;
    const bridgeDetailBackButton = fixture.nativeElement.querySelector('#bridgeDetailBack');
    bridgeDetailBackButton.click();
    expect(component.listPageRefreshForbackToDetailPage).toBeTruthy();
    expect(component.bridgeWorldListDisplay).toBeTruthy();
    expect(component.isFilterComponentInitWithApicall).toBeFalsy();
    expect(component.bridgeWorldDetailDisplay).toBeFalsy();
    expect(component.selectedKitIdList.length).toEqual(0);
    component.showOtsWorldListDisplay();
  });


});
