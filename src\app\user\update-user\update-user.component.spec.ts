import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { getMockCountryList } from '../../Tesing-Helper/TestCountryInfo';
import { fillAndSubmitForm, getMockUserDetail } from '../../Tesing-Helper/TestUserInfo';
import { commonsProviders } from '../../Tesing-Helper/test-utils';
import { ConfirmDialogService } from '../../confirmationdialog/confirmation.service';
import { UserResponse } from '../../model/User/UserResponse.model';
import { ExceptionHandlingService } from '../../shared/ExceptionHandling.service';
import { CountryCacheService } from '../../shared/Service/CacheService/countrycache.service';
import { RoleApiCallService } from '../../shared/Service/RoleService/role-api-call.service';
import { AuthJwtService } from '../../shared/auth-jwt.service';
import { HidePermissionNamePipe } from '../../shared/pipes/Role/hidePermissionName.pipe';
import { PrintListPipe } from '../../shared/pipes/printList.pipe';
import { UserApiCallService } from '../../shared/Service/user/user-api-call.service';
import { CommonsService } from '../../shared/util/commons.service';
import { UpdateUserComponent } from './update-user.component';

describe('UpdateUserComponent', () => {
  let component: UpdateUserComponent;
  let fixture: ComponentFixture<UpdateUserComponent>;
  //spy service
  let authServiceMock: any;

  //Mock service
  let searchMemberServiceMock: UserApiCallService;
  let exceptionService: ExceptionHandlingService;
  let roleApiCallServiceMock: RoleApiCallService;
  let countryCacheServiceMock: CountryCacheService;

  // spy object service
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let localStorageServiceMock: jasmine.SpyObj<LocalStorageService>;
  let dialogservice: ConfirmDialogService;

  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    authServiceMock = jasmine.createSpyObj('AuthJwtService', ['isAuthenticate', 'loginNavigate']);
    localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);

    // Mock the return value of retrieve for the getPermissionObject function
    localStorageServiceMock.retrieve.and.returnValue('mockedPermissionObject');  // Adjust this value as per your application logic

    await TestBed.configureTestingModule({
      declarations: [UpdateUserComponent, PrintListPipe],
      imports: [FormsModule,
        ReactiveFormsModule,
        NgMultiSelectDropDownModule.forRoot()],
      providers: [
        CommonsService,
        UserApiCallService,
        ConfirmDialogService,
        RoleApiCallService,
        HidePermissionNamePipe,
        ExceptionHandlingService,
        SessionStorageService,
        { provide: AuthJwtService, useValue: authServiceMock },
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(UpdateUserComponent);
    component = fixture.componentInstance;
    authServiceMock.isAuthenticate.and.returnValue(true);
    searchMemberServiceMock = TestBed.inject(UserApiCallService);
    exceptionService = TestBed.inject(ExceptionHandlingService);
    roleApiCallServiceMock = TestBed.inject(RoleApiCallService);
    countryCacheServiceMock = TestBed.inject(CountryCacheService);
    dialogservice = TestBed.inject(ConfirmDialogService);
    //model service spy 
    spyOn(dialogservice, 'confirm').and.returnValue(Promise.resolve(true));
    fixture.detectChanges();
  });

  const roleFilter = ["Cloud Admin", "Device / SV Team"];
  const countryFilter: any = getMockCountryList()

  const mockUserResponse: UserResponse = getMockUserDetail();

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load user detail on init when authenticated', async () => {
    // Mock API 
    spyOn(searchMemberServiceMock, 'getSingleMember')?.and.returnValue(of(new HttpResponse<UserResponse>({
      body: mockUserResponse,
      status: 200,
    })));
    spyOn(searchMemberServiceMock, 'updateMember')?.and.returnValue(of(new HttpResponse<UserResponse>({
      body: mockUserResponse,
      status: 200,
    })));
    spyOn(roleApiCallServiceMock, 'getRoleNameList')?.and.returnValue(Promise.resolve(roleFilter));
    spyOn(countryCacheServiceMock, 'getCountryListFromCache')?.and.returnValue(Promise.resolve(countryFilter));


    // Act: Initialize the component
    component.ngOnInit();
    await fixture.whenStable();
    fixture.detectChanges();
    expect(component.userResponse).toEqual(mockUserResponse);

    //user_role click
    spyOn(component, 'onItemSelectValidation').and.callThrough();
    component.form.get('userRoles').setValue([]);
    fixture.detectChanges();
    const userRoleField = fixture.nativeElement.querySelector('#update_user_role');
    userRoleField?.click();
    fixture.detectChanges();
    expect(component.onItemSelectValidation).toHaveBeenCalled();

    // Use the helper method to fill and submit the form
    await fillAndSubmitForm(component.form, fixture, '#updateUserBtn');
    expect(toastrServiceMock.success).toHaveBeenCalled();
  });

  it('should update user get user detail by id Exception', async () => {
    // Mock API
    spyOn(searchMemberServiceMock, 'getSingleMember')?.and.returnValue(of(new HttpResponse<UserResponse>({
      body: null,
      status: 204,
    })));
    spyOn(roleApiCallServiceMock, 'getRoleNameList')?.and.returnValue(Promise.resolve(roleFilter));
    spyOn(countryCacheServiceMock, 'getCountryListFromCache')?.and.returnValue(Promise.resolve(countryFilter));

    // Act: Initialize the component
    component.ngOnInit();
    await fixture.whenStable();
    fixture.detectChanges();
    expect(toastrServiceMock.warning).toHaveBeenCalled();
  });


  it('should failed load user detail ', async () => {
    const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });
    // Mock API
    spyOn(searchMemberServiceMock, 'getSingleMember')?.and.returnValue(throwError(() => mockError));

    // Act: Initialize the component
    component.ngOnInit();
    await fixture.whenStable();
    fixture.detectChanges();
    spyOn(exceptionService, 'customErrorMessage')?.and.callThrough();
  });

  it('should update user throw exception', async () => {
    // Mock API 
    spyOn(searchMemberServiceMock, 'getSingleMember')?.and.returnValue(of(new HttpResponse<UserResponse>({
      body: mockUserResponse,
      status: 200,
    })));
    const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });
    spyOn(searchMemberServiceMock, 'updateMember')?.and.returnValue(throwError(() => mockError));
    spyOn(roleApiCallServiceMock, 'getRoleNameList')?.and.returnValue(Promise.resolve(roleFilter));
    spyOn(countryCacheServiceMock, 'getCountryListFromCache')?.and.returnValue(Promise.resolve(countryFilter));


    // Act: Initialize the component
    component.ngOnInit();
    await fixture.whenStable();
    fixture.detectChanges();
    expect(component.userResponse).toEqual(mockUserResponse);

    //update user
    await fillAndSubmitForm(component.form, fixture, '#updateUserBtn');
    spyOn(exceptionService, 'customErrorMessage')?.and.callThrough();
  });

  it('should update user but user not found', async () => {
    // Mock API 
    spyOn(searchMemberServiceMock, 'getSingleMember')?.and.returnValue(of(new HttpResponse<UserResponse>({
      body: mockUserResponse,
      status: 200,
    })));
    spyOn(searchMemberServiceMock, 'updateMember')?.and.returnValue(of(new HttpResponse<UserResponse>({
      body: null,
      status: 204,
    })));
    spyOn(roleApiCallServiceMock, 'getRoleNameList')?.and.returnValue(Promise.resolve(roleFilter));
    spyOn(countryCacheServiceMock, 'getCountryListFromCache')?.and.returnValue(Promise.resolve(countryFilter));


    // Act: Initialize the component
    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();
    fixture.detectChanges();
    expect(component.userResponse).toEqual(mockUserResponse);

    // Use the helper method to fill and submit the form
    await fillAndSubmitForm(component.form, fixture, '#updateUserBtn');
    expect(toastrServiceMock.info).toHaveBeenCalled();
  });

});
